import 'package:flutter/foundation.dart';
import '../models/vehicle.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import 'api_error.dart';

class VehicleService {
  final ApiService _apiService = ApiService();

  // Set access token for authenticated requests
  void setAccessToken(String token) {
    _apiService.setAccessToken(token);
  }

  // Get vehicle by ID
  Future<Vehicle> getVehicleById(String vehicleId) async {
    if (kDebugMode) {
      print('🚙 VEHICLE: Getting vehicle by ID: $vehicleId');
    }

    try {
      final response = await _apiService.get('/api/vehicles/$vehicleId');

      if (kDebugMode) {
        print('🚙 VEHICLE: Vehicle response: $response');
      }

      final apiResponse = ApiResponse<Vehicle>.fromJson(
        response,
        (data) => Vehicle.fromJson(data as Map<String, dynamic>),
      );

      if (apiResponse.success && apiResponse.data != null) {
        return apiResponse.data!;
      } else {
        throw ApiException(ApiError(
          message: 'Failed to get vehicle',
          details: apiResponse.error ?? apiResponse.message,
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('🚙 VEHICLE: Error getting vehicle: $e');
      }
      rethrow;
    }
  }

  // Create a new vehicle
  Future<Vehicle> createVehicle({
    required String model,
    required int year,
    required String plateNumber,
    required VehicleStatus status,
    required FuelType fuelType,
    required double fuelLevel,
    required DateTime insuranceExpiry,
    String? driverId,
  }) async {
    if (kDebugMode) {
      print('🚙 VEHICLE: Creating new vehicle: $model');
    }

    try {
      // Try different data formats to work around server issues
      final vehicleData = <String, dynamic>{
        'model': model,
        'year': year,
        'plateNumber': plateNumber,
        'status': status.name, // Try uppercase first
        'fuelType': fuelType.name, // Try uppercase first
        'fuelLevel': fuelLevel,
        'insuranceExpiry': insuranceExpiry.toIso8601String(),
      };

      // Add driverId if provided
      if (driverId != null) {
        vehicleData['driverId'] = driverId;
      }

      if (kDebugMode) {
        print('🚙 VEHICLE: Sending data: $vehicleData');
      }

      // First attempt with current format
      dynamic response;
      try {
        response = await _apiService.post('/api/vehicles', vehicleData);
      } catch (e) {
        if (kDebugMode) {
          print('🚙 VEHICLE: First attempt failed, trying lowercase enums...');
        }

        // Try with lowercase enum values
        vehicleData['status'] = status.name.toLowerCase();
        vehicleData['fuelType'] = fuelType.name.toLowerCase();

        if (kDebugMode) {
          print('🚙 VEHICLE: Retry with data: $vehicleData');
        }

        response = await _apiService.post('/api/vehicles', vehicleData);
      }

      if (kDebugMode) {
        print('🚙 VEHICLE: Create vehicle response: $response');
      }

      final apiResponse = ApiResponse<Vehicle>.fromJson(
        response,
        (data) => Vehicle.fromJson(data as Map<String, dynamic>),
      );

      if (apiResponse.success && apiResponse.data != null) {
        return apiResponse.data!;
      } else {
        throw ApiException(ApiError(
          message: 'Failed to create vehicle',
          details: apiResponse.error ?? apiResponse.message,
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('🚙 VEHICLE: Error creating vehicle: $e');
      }
      rethrow;
    }
  }

  // Delete a vehicle
  Future<void> deleteVehicle(String vehicleId) async {
    if (kDebugMode) {
      print('🚙 VEHICLE: Deleting vehicle: $vehicleId');
    }

    try {
      final response = await _apiService.delete('/api/vehicles/$vehicleId');

      if (kDebugMode) {
        print('🚙 VEHICLE: Delete vehicle response: $response');
      }

      final apiResponse = ApiResponse<dynamic>.fromJson(
        response,
        (data) => data,
      );

      if (!apiResponse.success) {
        throw ApiException(ApiError(
          message: 'Failed to delete vehicle',
          details: apiResponse.error ?? apiResponse.message,
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('🚙 VEHICLE: Error deleting vehicle: $e');
      }
      rethrow;
    }
  }

  // Set vehicle as active
  Future<void> setActiveVehicle(String vehicleId) async {
    if (kDebugMode) {
      print('🚙 VEHICLE: Setting vehicle as active: $vehicleId');
    }

    try {
      final response = await _apiService.put(
        '/api/vehicles/$vehicleId/active',
        {'status': VehicleStatus.ACTIVE.name},
      );

      if (kDebugMode) {
        print('🚙 VEHICLE: Set active vehicle response: $response');
      }

      final apiResponse = ApiResponse<dynamic>.fromJson(
        response,
        (data) => data,
      );

      if (!apiResponse.success) {
        throw ApiException(ApiError(
          message: 'Failed to set vehicle as active',
          details: apiResponse.error ?? apiResponse.message,
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('🚙 VEHICLE: Error setting vehicle as active: $e');
      }
      rethrow;
    }
  }

  Future<void> updateVehicle({
    required String id,
    required String model,
    required int year,
    required String plateNumber,
    required VehicleStatus status,
    required FuelType fuelType,
    required double fuelLevel,
    required DateTime insuranceExpiry,
  }) async {
    if (kDebugMode) {
      print('🚙 VEHICLE: Updating vehicle: $id');
    }

    try {
      final vehicleData = {
        'model': model,
        'year': year,
        'plateNumber': plateNumber,
        'status': status.name,
        'fuelType': fuelType.name,
        'fuelLevel': fuelLevel,
        'insuranceExpiry': insuranceExpiry.toIso8601String(),
      };

      if (kDebugMode) {
        print('🚙 VEHICLE: Sending update data: $vehicleData');
      }

      final response = await _apiService.put(
        '/api/vehicles/$id',
        vehicleData,
      );

      if (kDebugMode) {
        print('🚙 VEHICLE: Update vehicle response: $response');
      }

      final apiResponse = ApiResponse<dynamic>.fromJson(
        response,
        (data) => data,
      );

      if (!apiResponse.success) {
        throw ApiException(ApiError(
          message: 'Failed to update vehicle',
          details: apiResponse.error ?? apiResponse.message,
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('🚙 VEHICLE: Error updating vehicle: $e');
      }
      rethrow;
    }
  }
}
