import 'package:flutter/material.dart';
import 'utils/responsive_size.dart';
import 'utils/app_theme.dart';
import 'widgets/open_street_map.dart';

class MapScreen extends StatelessWidget {
  final String title;
  final double initialLatitude;
  final double initialLongitude;
  final double zoom;
  final List<MapMarker>? markers;
  final MapRoute? route;

  const MapScreen({
    super.key,
    required this.title,
    this.initialLatitude = 24.7136, // Default to Riyadh, Saudi Arabia
    this.initialLongitude = 46.6753,
    this.zoom = 13.0,
    this.markers,
    this.route,
  });

  @override
  Widget build(BuildContext context) {
    ResponsiveSize().init(context);

    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      body: SafeArea(
        child: Directionality(
          textDirection: TextDirection.rtl,
          child: Column(
            children: [
              const SizedBox(height: 16), // Consistent spacing
              // Enhanced Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0), // Consistent padding
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: Container(
                        padding: const EdgeInsets.all(AppTheme.spacingSmall),
                        decoration: AppTheme.getCardDecoration(), // Consistent decoration
                        child: Icon(Icons.arrow_back_ios_new, 
                          color: AppTheme.primaryColor,
                          size: AppTheme.iconSizeSmall,
                        ),
                      ),
                    ),
                    Text(
                      title,
                      style: AppTheme.getTitleStyle(), // Consistent title style
                    ),
                    IconButton(
                      icon: Container(
                         padding: const EdgeInsets.all(AppTheme.spacingSmall),
                         decoration: AppTheme.getCardDecoration(), // Consistent decoration
                        child: const Icon(Icons.share,
                         color: AppTheme.primaryColor,
                         size: AppTheme.iconSizeSmall,
                        ),
                      ),
                      onPressed: () {
                        // Share functionality
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('مشاركة الموقع'),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24), // Consistent spacing
              Expanded(
                child: OpenStreetMapWidget(
                  initialLatitude: initialLatitude,
                  initialLongitude: initialLongitude,
                  zoom: zoom,
                  markers: markers,
                  route: route,
                  height: double.infinity,
                  showAttribution: true,
                  interactive: true,
                ),
              ),
              Container(
                padding: const EdgeInsets.all(24), // Consistent padding
                decoration: BoxDecoration(
                  color: AppTheme.backgroundWhite,
                   borderRadius: const BorderRadius.only(
                     topLeft: Radius.circular(24),
                     topRight: Radius.circular(24),
                   ), // Rounded top corners
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, -10),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12), // Consistent padding
                          decoration: BoxDecoration(
                            color: AppTheme.primaryLightColor,
                            borderRadius: BorderRadius.circular(12), // Rounded corners
                          ),
                          child: const Icon(
                            Icons.location_on,
                            color: AppTheme.primaryColor,
                            size: 24, // Consistent icon size
                          ),
                        ),
                        const SizedBox(width: 16), // Consistent spacing
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'من: شارع الملك فهد',
                                style: AppTheme.getBodyStyle(), // Consistent text style
                              ),
                              const SizedBox(height: 4), // Consistent spacing
                              Text(
                                'إلى: طريق الأمير محمد بن سلمان',
                                style: AppTheme.getBodyStyle(), // Consistent text style
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24), // Consistent spacing
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildInfoItem(
                          icon: Icons.access_time,
                          label: 'الوقت',
                          value: '25 دقيقة',
                        ),
                        _buildInfoItem(
                          icon: Icons.speed,
                          label: 'المسافة',
                          value: '12.5 كم',
                        ),
                        _buildInfoItem(
                          icon: Icons.local_gas_station,
                          label: 'الوقود',
                          value: '1.2 لتر',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12), // Consistent padding
          decoration: BoxDecoration(
            color: AppTheme.primaryLightColor,
             borderRadius: BorderRadius.circular(12), // Rounded corners
          ),
          child: Icon(
            icon,
            color: AppTheme.primaryColor,
            size: 24, // Consistent icon size
          ),
        ),
        const SizedBox(height: 8), // Consistent spacing
        Text(
          label,
          style: AppTheme.getBodyStyle(), // Consistent text style
        ),
        const SizedBox(height: 4), // Consistent spacing
        Text(
          value,
          style: AppTheme.getSubtitleStyle(), // Consistent text style
        ),
      ],
    );
  }
}
