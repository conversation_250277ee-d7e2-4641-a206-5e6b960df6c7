import 'package:flutter/material.dart';
import '../services/bluetooth_permissions.dart';
import '../utils/app_theme.dart';
import '../utils/responsive_size.dart';

class BluetoothPermissionDialog extends StatefulWidget {
  const BluetoothPermissionDialog({super.key});

  @override
  State<BluetoothPermissionDialog> createState() => _BluetoothPermissionDialogState();
}

class _BluetoothPermissionDialogState extends State<BluetoothPermissionDialog> {
  bool _isRequesting = false;
  BluetoothPermissionResult? _permissionResult;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  Future<void> _checkPermissions() async {
    final result = await BluetoothPermissions.checkPermissions();
    if (mounted) {
      setState(() {
        _permissionResult = result;
      });
    }
  }

  Future<void> _requestPermissions() async {
    setState(() {
      _isRequesting = true;
    });

    try {
      final result = await BluetoothPermissions.requestPermissions();
      if (mounted) {
        setState(() {
          _permissionResult = result;
          _isRequesting = false;
        });

        if (result.status == BluetoothPermissionStatus.granted) {
          Navigator.of(context).pop(true);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isRequesting = false;
          _permissionResult = BluetoothPermissionResult(
            status: BluetoothPermissionStatus.denied,
            message: 'خطأ في طلب الأذونات: ${e.toString()}',
          );
        });
      }
    }
  }

  Future<void> _enableBluetooth() async {
    setState(() {
      _isRequesting = true;
    });

    try {
      bool enabled = await BluetoothPermissions.enableBluetooth();
      if (mounted) {
        setState(() {
          _isRequesting = false;
        });

        if (enabled) {
          await _checkPermissions();
        } else {
          setState(() {
            _permissionResult = BluetoothPermissionResult(
              status: BluetoothPermissionStatus.bluetoothDisabled,
              message: 'فشل في تفعيل البلوتوث. يرجى تفعيله يدوياً',
            );
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isRequesting = false;
          _permissionResult = BluetoothPermissionResult(
            status: BluetoothPermissionStatus.bluetoothDisabled,
            message: 'خطأ في تفعيل البلوتوث: ${e.toString()}',
          );
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Initialize ResponsiveSize if not already initialized
    if (!ResponsiveSize.isInitialized) {
      ResponsiveSize().init(context);
    }

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * (ResponsiveSize.isTablet ? 0.6 : 0.9),
        constraints: BoxConstraints(
          maxWidth: ResponsiveSize.isTablet ? 500 : double.infinity,
        ),
        padding: EdgeInsets.all(ResponsiveSize.isTablet ? 20 : 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(ResponsiveSize.isTablet ? 20 : 16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            SizedBox(height: ResponsiveSize.isTablet ? 20 : 16),
            _buildContent(),
            SizedBox(height: ResponsiveSize.isTablet ? 24 : 20),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(ResponsiveSize.isTablet ? 12 : 10),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(ResponsiveSize.isTablet ? 12 : 10),
          ),
          child: Icon(
            Icons.bluetooth,
            color: AppTheme.primaryColor,
            size: ResponsiveSize.isTablet ? 24 : 20,
          ),
        ),
        SizedBox(width: ResponsiveSize.isTablet ? 16 : 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'أذونات البلوتوث',
                style: TextStyle(
                  fontSize: ResponsiveSize.isTablet ? 18 : 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textDark,
                ),
              ),
              Text(
                'مطلوبة للاتصال بأجهزة OBD-II',
                style: TextStyle(
                  fontSize: ResponsiveSize.isTablet ? 14 : 13,
                  color: AppTheme.textMedium,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    if (_permissionResult == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStatusIndicator(),
        SizedBox(height: ResponsiveSize.isTablet ? 16 : 12),
        _buildMessage(),
        if (_permissionResult!.deniedPermissions.isNotEmpty) ...[
          SizedBox(height: ResponsiveSize.isTablet ? 16 : 12),
          _buildPermissionsList(),
        ],
      ],
    );
  }

  Widget _buildStatusIndicator() {
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (_permissionResult!.status) {
      case BluetoothPermissionStatus.granted:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = 'جميع الأذونات مفعلة';
        break;
      case BluetoothPermissionStatus.bluetoothDisabled:
        statusColor = Colors.orange;
        statusIcon = Icons.bluetooth_disabled;
        statusText = 'البلوتوث غير مفعل';
        break;
      case BluetoothPermissionStatus.permanentlyDenied:
        statusColor = Colors.red;
        statusIcon = Icons.block;
        statusText = 'تم رفض الأذونات نهائياً';
        break;
      default:
        statusColor = Colors.orange;
        statusIcon = Icons.warning;
        statusText = 'أذونات مطلوبة';
    }

    return Container(
      padding: EdgeInsets.all(ResponsiveSize.isTablet ? 12 : 10),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(ResponsiveSize.isTablet ? 10 : 8),
        border: Border.all(
          color: statusColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            statusIcon,
            color: statusColor,
            size: ResponsiveSize.isTablet ? 20 : 18,
          ),
          SizedBox(width: ResponsiveSize.isTablet ? 12 : 8),
          Expanded(
            child: Text(
              statusText,
              style: TextStyle(
                fontSize: ResponsiveSize.isTablet ? 14 : 13,
                fontWeight: FontWeight.w600,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessage() {
    return Text(
      _permissionResult!.message,
      style: TextStyle(
        fontSize: ResponsiveSize.isTablet ? 14 : 13,
        color: AppTheme.textMedium,
        height: 1.4,
      ),
    );
  }

  Widget _buildPermissionsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأذونات المطلوبة:',
          style: TextStyle(
            fontSize: ResponsiveSize.isTablet ? 14 : 13,
            fontWeight: FontWeight.w600,
            color: AppTheme.textDark,
          ),
        ),
        SizedBox(height: ResponsiveSize.isTablet ? 8 : 6),
        ..._permissionResult!.deniedPermissions.map((permission) {
          return Padding(
            padding: EdgeInsets.only(bottom: ResponsiveSize.isTablet ? 4 : 2),
            child: Row(
              children: [
                Icon(
                  Icons.circle,
                  size: ResponsiveSize.isTablet ? 6 : 4,
                  color: AppTheme.textMedium,
                ),
                SizedBox(width: ResponsiveSize.isTablet ? 8 : 6),
                Expanded(
                  child: Text(
                    BluetoothPermissions.getPermissionDisplayName(permission),
                    style: TextStyle(
                      fontSize: ResponsiveSize.isTablet ? 13 : 12,
                      color: AppTheme.textMedium,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildActions() {
    if (_permissionResult == null) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        Expanded(
          child: TextButton(
            onPressed: _isRequesting ? null : () => Navigator.of(context).pop(false),
            child: Text(
              'إلغاء',
              style: TextStyle(
                fontSize: ResponsiveSize.isTablet ? 14 : 13,
                color: AppTheme.textMedium,
              ),
            ),
          ),
        ),
        SizedBox(width: ResponsiveSize.isTablet ? 12 : 8),
        Expanded(
          flex: 2,
          child: _buildPrimaryAction(),
        ),
      ],
    );
  }

  Widget _buildPrimaryAction() {
    String buttonText;
    VoidCallback? onPressed;

    switch (_permissionResult!.status) {
      case BluetoothPermissionStatus.granted:
        buttonText = 'متابعة';
        onPressed = _isRequesting ? null : () => Navigator.of(context).pop(true);
        break;
      case BluetoothPermissionStatus.bluetoothDisabled:
        buttonText = 'تفعيل البلوتوث';
        onPressed = _isRequesting ? null : _enableBluetooth;
        break;
      case BluetoothPermissionStatus.permanentlyDenied:
        buttonText = 'فتح الإعدادات';
        onPressed = _isRequesting ? null : () async {
          await BluetoothPermissions.openSettings();
          Navigator.of(context).pop(false);
        };
        break;
      default:
        buttonText = 'منح الأذونات';
        onPressed = _isRequesting ? null : _requestPermissions;
    }

    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(vertical: ResponsiveSize.isTablet ? 14 : 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ResponsiveSize.isTablet ? 10 : 8),
        ),
      ),
      child: _isRequesting
          ? SizedBox(
              width: ResponsiveSize.isTablet ? 20 : 16,
              height: ResponsiveSize.isTablet ? 20 : 16,
              child: const CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(
              buttonText,
              style: TextStyle(
                fontSize: ResponsiveSize.isTablet ? 14 : 13,
                fontWeight: FontWeight.w600,
              ),
            ),
    );
  }
}

/// Helper function to show the Bluetooth permission dialog
Future<bool?> showBluetoothPermissionDialog(BuildContext context) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => const BluetoothPermissionDialog(),
  );
}
