import 'package:flutter/foundation.dart';
import '../models/trip.dart';
import '../models/driver.dart';
import '../models/reports_data.dart';
import 'reports_storage_service.dart';
import 'trip_service.dart';
import 'driver_service.dart';
import 'auth_service.dart';

class ReportsService {
  final ReportsStorageService _storageService = ReportsStorageService();
  final TripService _tripService = TripService();
  final DriverService _driverService = DriverService();
  final AuthService _authService = AuthService();

  // Set access token for API services
  void setAccessToken(String token) {
    _tripService.setAccessToken(token);
    _driverService.setAccessToken(token);
  }

  // Get reports data for a specific period
  Future<ReportsData> getReportsData(
      String driverId, ReportPeriod period) async {
    try {
      if (kDebugMode) {
        print(
            '📊 REPORTS: Getting reports data for $driverId, period: ${period.name}');
      }

      // FORCE fresh data from API to ensure synchronization (skip cache)
      if (kDebugMode) {
        print(
            '📊 REPORTS: FORCING fresh data from API (cache disabled for sync)');
      }

      return await _fetchAndCacheReportsData(driverId, period);
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS: Error getting reports data: $e');
      }

      // Fallback to cached data if available
      final (periodStart, periodEnd) = _getPeriodDates(period);
      final cachedData = await _storageService.getStoredReportsData(
          driverId, period.name, periodStart, periodEnd);

      if (cachedData != null) {
        return await _buildReportsDataFromCache(driverId, period, cachedData);
      }

      // Return empty data if no cache available
      return _createEmptyReportsData(driverId, period);
    }
  }

  // Fetch fresh data from API and cache it
  Future<ReportsData> _fetchAndCacheReportsData(
      String driverId, ReportPeriod period) async {
    if (kDebugMode) {
      print('📊 REPORTS: Fetching fresh data from API');
    }

    // Get access token
    final accessToken = await _authService.getAccessToken();
    if (accessToken == null) {
      throw Exception('No access token found');
    }
    setAccessToken(accessToken);

    // Fetch driver and trips data
    final driver = await _driverService.getCurrentUserDriver();
    final trips = await _tripService.getDriverTrips(driverId);

    // FORCE synchronize cache with fresh API data
    await _forceSyncCacheWithAPI(driverId, trips, driver);

    // Filter trips for the selected period
    final (periodStart, periodEnd) = _getPeriodDates(period);

    if (kDebugMode) {
      print('📊 REPORTS: Period ${period.name}: $periodStart to $periodEnd');
      print('📊 REPORTS: Total trips fetched: ${trips.length}');
    }

    final periodTrips = trips.where((trip) {
      final isInPeriod = (trip.startTime.isAfter(periodStart) ||
              trip.startTime.isAtSameMomentAs(periodStart)) &&
          (trip.startTime.isBefore(periodEnd) ||
              trip.startTime.isAtSameMomentAs(periodEnd));

      if (kDebugMode) {
        if (isInPeriod) {
          print(
              '✅ TRIP IN PERIOD: ${trip.id} - ${trip.startTime} - Distance: ${trip.distance}km, Fuel: ${trip.fuelConsumed}L');
        } else {
          print(
              '❌ TRIP OUT OF PERIOD: ${trip.id} - ${trip.startTime} (Period: $periodStart to $periodEnd)');
        }
      }

      return isInPeriod;
    }).toList();

    if (kDebugMode) {
      print('📊 REPORTS: Trips in period: ${periodTrips.length}');
    }

    // Calculate aggregated data
    final aggregatedData = _calculateAggregatedData(periodTrips);

    // Store aggregated data
    await _storageService.storeReportsData(
        driverId, period.name, periodStart, periodEnd, aggregatedData);

    // Build and return reports data
    return _buildReportsData(driverId, period, periodTrips, driver);
  }

  // Build reports data from cached information
  Future<ReportsData> _buildReportsDataFromCache(String driverId,
      ReportPeriod period, Map<String, dynamic> cachedData) async {
    final (periodStart, periodEnd) = _getPeriodDates(period);
    final trips = await _storageService.getTripsForPeriod(
        driverId, periodStart, periodEnd);
    final driver = await _storageService.getCachedDriver(driverId);

    return _buildReportsData(driverId, period, trips, driver);
  }

  // Build comprehensive reports data
  ReportsData _buildReportsData(
      String driverId, ReportPeriod period, List<Trip> trips, Driver? driver) {
    final (periodStart, periodEnd) = _getPeriodDates(period);
    final aggregatedData = _calculateAggregatedData(trips);

    // Generate daily data for charts
    final dailyData = _generateDailyData(trips, periodStart, periodEnd, period);
    final fuelData = _generateFuelData(trips, periodStart, periodEnd, period);
    final performanceData =
        _generatePerformanceData(trips, driver, periodStart, periodEnd, period);

    return ReportsData(
      driverId: driverId,
      period: period,
      periodStart: periodStart,
      periodEnd: periodEnd,
      totalTrips: aggregatedData['totalTrips'],
      totalDistance: aggregatedData['totalDistance'],
      totalFuel: aggregatedData['totalFuel'],
      totalDuration: Duration(seconds: aggregatedData['totalDuration']),
      avgFuelEfficiency: aggregatedData['avgFuelEfficiency'],
      avgSpeed: aggregatedData['avgSpeed'],
      safetyScore: driver?.driverScore ?? 0.0,
      dailyData: dailyData,
      fuelData: fuelData,
      performanceData: performanceData,
      lastUpdated: DateTime.now(),
    );
  }

  // Calculate aggregated data from trips
  Map<String, dynamic> _calculateAggregatedData(List<Trip> trips) {
    if (kDebugMode) {
      print(
          '📊 REPORTS: Calculating aggregated data for ${trips.length} trips');
      print('📊 TRIPS TO AGGREGATE:');
      for (final trip in trips) {
        print(
            '📊   - ${trip.id}: Distance=${trip.distance}km, Fuel=${trip.fuelConsumed}L, Time=${trip.startTime}');
      }
    }

    if (trips.isEmpty) {
      if (kDebugMode) {
        print('📊 REPORTS: No trips found, returning empty data');
      }
      return {
        'totalTrips': 0,
        'totalDistance': 0.0,
        'totalFuel': 0.0,
        'totalDuration': 0,
        'avgFuelEfficiency': 0.0,
        'avgSpeed': 0.0,
        'safetyScore': 0.0,
      };
    }

    double totalDistance = 0.0;
    double totalFuel = 0.0;
    Duration totalDuration = Duration.zero;

    // Debug individual trips
    for (final trip in trips) {
      final distance = trip.distance ?? 0.0;
      final fuel = trip.fuelConsumed ?? 0.0; // Use 0 if null as requested
      final duration = trip.duration ?? Duration.zero;

      totalDistance += distance;
      totalFuel += fuel;
      totalDuration += duration;

      if (kDebugMode) {
        print(
            '📊 TRIP AGGREGATION: ${trip.id} - Distance: ${distance}km, Fuel: ${fuel}L, Duration: ${duration.inMinutes}min, Status: ${trip.status.name}');
        print(
            '📊 RUNNING TOTALS: Distance: ${totalDistance}km, Fuel: ${totalFuel}L');
        if (trip.distance == null) {
          print('⚠️ WARNING: Trip ${trip.id} has null distance - using 0.0km');
        }
        if (trip.fuelConsumed == null) {
          print('⚠️ WARNING: Trip ${trip.id} has null fuel - using 0.0L');
        }
      }
    }

    final avgFuelEfficiency = totalFuel > 0 ? (totalDistance / totalFuel) : 0.0;
    final avgSpeed = totalDuration.inHours > 0
        ? (totalDistance / totalDuration.inHours)
        : 0.0;

    if (kDebugMode) {
      print('📊 REPORTS: Total Distance: ${totalDistance}km');
      print('📊 REPORTS: Total Fuel: ${totalFuel}L');
      print('📊 REPORTS: Total Duration: ${totalDuration.inMinutes}min');
      print('📊 REPORTS: Avg Fuel Efficiency: ${avgFuelEfficiency}km/L');
      print('📊 REPORTS: Avg Speed: ${avgSpeed}km/h');
    }

    return {
      'totalTrips': trips.length,
      'totalDistance': totalDistance,
      'totalFuel': totalFuel,
      'totalDuration': totalDuration.inSeconds,
      'avgFuelEfficiency': avgFuelEfficiency,
      'avgSpeed': avgSpeed,
      'safetyScore': 0.0, // Will be set from driver data
    };
  }

  // Generate daily data for charts based on period
  List<DailyData> _generateDailyData(
      List<Trip> trips, DateTime start, DateTime end, ReportPeriod period) {
    final dataMap = <String, DailyData>{};

    if (period == ReportPeriod.daily) {
      // For daily: show each hour
      for (var hour = 0; hour < 24; hour++) {
        final hourDate = DateTime(start.year, start.month, start.day, hour);
        final hourKey = 'hour_$hour';
        dataMap[hourKey] = DailyData(
          date: hourDate,
          trips: 0,
          distance: 0.0,
          fuel: 0.0,
          duration: Duration.zero,
        );
      }
    } else if (period == ReportPeriod.weekly) {
      // For weekly: show each day
      for (var date = start;
          date.isBefore(end);
          date = date.add(const Duration(days: 1))) {
        final dateKey =
            '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
        dataMap[dateKey] = DailyData(
          date: date,
          trips: 0,
          distance: 0.0,
          fuel: 0.0,
          duration: Duration.zero,
        );
      }
    } else if (period == ReportPeriod.monthly) {
      // For monthly: show each week
      var weekStart = start;
      while (weekStart.isBefore(end)) {
        var weekEnd = weekStart.add(const Duration(days: 7));
        if (weekEnd.isAfter(end)) weekEnd = end;

        final weekKey = 'week_${weekStart.day}';
        dataMap[weekKey] = DailyData(
          date: weekStart,
          trips: 0,
          distance: 0.0,
          fuel: 0.0,
          duration: Duration.zero,
        );
        weekStart = weekEnd;
      }
    } else {
      // For yearly: show each month
      for (var month = start.month;
          month <= 12 && DateTime(start.year, month, 1).isBefore(end);
          month++) {
        final monthDate = DateTime(start.year, month, 1);
        final monthKey =
            '${monthDate.year}-${month.toString().padLeft(2, '0')}';
        dataMap[monthKey] = DailyData(
          date: monthDate,
          trips: 0,
          distance: 0.0,
          fuel: 0.0,
          duration: Duration.zero,
        );
      }
    }

    // Aggregate trip data
    for (final trip in trips) {
      String dateKey;

      if (period == ReportPeriod.daily) {
        // Hourly aggregation for daily view
        dateKey = 'hour_${trip.startTime.hour}';
      } else if (period == ReportPeriod.weekly) {
        // Daily aggregation for weekly view
        dateKey =
            '${trip.startTime.year}-${trip.startTime.month.toString().padLeft(2, '0')}-${trip.startTime.day.toString().padLeft(2, '0')}';
      } else if (period == ReportPeriod.monthly) {
        // Weekly aggregation for monthly view
        final weekStart =
            trip.startTime.subtract(Duration(days: trip.startTime.weekday - 1));
        dateKey = 'week_${weekStart.day}';
      } else {
        // Monthly aggregation for yearly view
        dateKey =
            '${trip.startTime.year}-${trip.startTime.month.toString().padLeft(2, '0')}';
      }

      final existing = dataMap[dateKey];
      if (existing != null) {
        dataMap[dateKey] = DailyData(
          date: existing.date,
          trips: existing.trips + 1,
          distance: existing.distance + (trip.distance ?? 0),
          fuel: existing.fuel + (trip.fuelConsumed ?? 0),
          duration: existing.duration + (trip.duration ?? Duration.zero),
        );
      }
    }

    return dataMap.values.toList()..sort((a, b) => a.date.compareTo(b.date));
  }

  // Generate fuel data for charts
  List<FuelData> _generateFuelData(
      List<Trip> trips, DateTime start, DateTime end, ReportPeriod period) {
    final fuelMap = <String, FuelData>{};

    for (final trip in trips) {
      final dateKey =
          '${trip.startTime.year}-${trip.startTime.month.toString().padLeft(2, '0')}-${trip.startTime.day.toString().padLeft(2, '0')}';
      final consumed = trip.fuelConsumed ?? 0;
      final distance = trip.distance ?? 0;
      final efficiency =
          distance > 0 && consumed > 0 ? distance / consumed : 0.0;

      final existing = fuelMap[dateKey];
      if (existing != null) {
        fuelMap[dateKey] = FuelData(
          date: existing.date,
          consumed: existing.consumed + consumed,
          efficiency:
              (existing.efficiency + efficiency) / 2, // Average efficiency
          cost: existing.cost + (consumed * 2.5), // Estimated cost per liter
        );
      } else {
        fuelMap[dateKey] = FuelData(
          date: trip.startTime,
          consumed: consumed,
          efficiency: efficiency,
          cost: consumed * 2.5, // Estimated cost per liter
        );
      }
    }

    return fuelMap.values.toList()..sort((a, b) => a.date.compareTo(b.date));
  }

  // Generate performance data for charts
  List<PerformanceData> _generatePerformanceData(List<Trip> trips,
      Driver? driver, DateTime start, DateTime end, ReportPeriod period) {
    final performanceMap = <String, PerformanceData>{};
    final baseScore = driver?.driverScore ?? 75.0;

    for (final trip in trips) {
      final dateKey =
          '${trip.startTime.year}-${trip.startTime.month.toString().padLeft(2, '0')}-${trip.startTime.day.toString().padLeft(2, '0')}';
      final distance = trip.distance ?? 0;
      final duration = trip.duration ?? Duration.zero;
      final avgSpeed = duration.inHours > 0 ? distance / duration.inHours : 0.0;
      final fuelEfficiency = (trip.fuelConsumed ?? 0) > 0
          ? distance / (trip.fuelConsumed ?? 1)
          : 0.0;

      // Simulate safety metrics based on speed and efficiency
      final hardBrakes = (avgSpeed > 80) ? 2 : 0;
      final rapidAccelerations = (avgSpeed > 70) ? 1 : 0;
      final safetyScore =
          baseScore - (hardBrakes * 2) - (rapidAccelerations * 1);

      performanceMap[dateKey] = PerformanceData(
        date: trip.startTime,
        safetyScore: safetyScore.clamp(0, 100),
        avgSpeed: avgSpeed,
        fuelEfficiency: fuelEfficiency,
        hardBrakes: hardBrakes,
        rapidAccelerations: rapidAccelerations,
      );
    }

    return performanceMap.values.toList()
      ..sort((a, b) => a.date.compareTo(b.date));
  }

  // Get period start and end dates
  (DateTime, DateTime) _getPeriodDates(ReportPeriod period) {
    final now = DateTime.now();

    switch (period) {
      case ReportPeriod.daily:
        final startOfDay = DateTime(now.year, now.month, now.day);
        final endOfDay = DateTime(now.year, now.month, now.day + 1);
        return (startOfDay, endOfDay);

      case ReportPeriod.weekly:
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        return (
          DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day),
          DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day + 7),
        );

      case ReportPeriod.monthly:
        final startOfMonth = DateTime(now.year, now.month, 1);
        final endOfMonth = DateTime(now.year, now.month + 1, 1);
        return (startOfMonth, endOfMonth);

      case ReportPeriod.yearly:
        final startOfYear = DateTime(now.year, 1, 1);
        final endOfYear = DateTime(now.year + 1, 1, 1);
        return (startOfYear, endOfYear);
    }
  }

  // Create empty reports data
  ReportsData _createEmptyReportsData(String driverId, ReportPeriod period) {
    final (periodStart, periodEnd) = _getPeriodDates(period);

    return ReportsData(
      driverId: driverId,
      period: period,
      periodStart: periodStart,
      periodEnd: periodEnd,
      totalTrips: 0,
      totalDistance: 0.0,
      totalFuel: 0.0,
      totalDuration: Duration.zero,
      avgFuelEfficiency: 0.0,
      avgSpeed: 0.0,
      safetyScore: 0.0,
      dailyData: [],
      fuelData: [],
      performanceData: [],
      lastUpdated: DateTime.now(),
    );
  }

  // Clear old cached data
  Future<void> clearOldCache() async {
    await _storageService.clearOldCache();
  }

  // Force refresh trip data from API and update cache
  Future<void> refreshTripDataFromAPI(String driverId) async {
    try {
      if (kDebugMode) {
        print(
            '📊 REPORTS: Force refreshing trip data from API for driver: $driverId');
      }

      // Get fresh trips from API
      final freshTrips = await _tripService.getDriverTrips(driverId);

      if (kDebugMode) {
        print('📊 REPORTS: Fetched ${freshTrips.length} fresh trips from API');
      }

      // Update cache with fresh data
      for (final trip in freshTrips) {
        await _storageService.refreshTripData(trip.id, trip);
      }

      if (kDebugMode) {
        print('📊 REPORTS: Updated cache with fresh trip data');
      }
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS: Error refreshing trip data from API: $e');
      }
    }
  }

  // Force synchronize cache with API data
  Future<void> _forceSyncCacheWithAPI(
      String driverId, List<Trip> freshTrips, Driver driver) async {
    try {
      if (kDebugMode) {
        print(
            '📊 REPORTS: Force synchronizing cache with ${freshTrips.length} fresh trips from API');
      }

      // Clear ALL cache first to ensure complete sync
      await _storageService.clearAllCache();

      // Cache fresh driver data
      await _storageService.cacheDriver(driver);

      // Cache fresh trips data with detailed logging
      for (final trip in freshTrips) {
        if (kDebugMode) {
          print(
              '📊 CACHE SYNC: Trip ${trip.id} - Distance: ${trip.distance}km, Fuel: ${trip.fuelConsumed}L, Status: ${trip.status.name}');
        }
      }

      await _storageService.cacheTrips(freshTrips);

      if (kDebugMode) {
        print('📊 REPORTS: Cache synchronization completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS: Error synchronizing cache with API: $e');
      }
    }
  }

  // Clear cache and force refresh
  Future<void> clearCacheAndRefresh(String driverId) async {
    try {
      await _storageService.clearAllCache();
      await refreshTripDataFromAPI(driverId);
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS: Error clearing cache and refreshing: $e');
      }
    }
  }

  // Fix null values in completed trips
  Future<void> fixNullTripValues(String driverId) async {
    try {
      if (kDebugMode) {
        print('📊 REPORTS: Fixing null values in completed trips...');
      }

      // Get all trips from API
      final trips = await _tripService.getDriverTrips(driverId);

      int fixedCount = 0;
      for (final trip in trips) {
        if (trip.status.name == 'COMPLETED') {
          bool needsUpdate = false;
          double? distance = trip.distance;
          double? fuel = trip.fuelConsumed;

          // Fix null fuel values for completed trips
          if (trip.fuelConsumed == null) {
            fuel = 0.0; // Set to 0 as requested
            needsUpdate = true;
            if (kDebugMode) {
              print('🔧 FIXING: Trip ${trip.id} - Setting fuel to 0.0L');
            }
          }

          // Fix null distance values for completed trips
          if (trip.distance == null) {
            distance = 0.0; // Set to 0 as requested
            needsUpdate = true;
            if (kDebugMode) {
              print('🔧 FIXING: Trip ${trip.id} - Setting distance to 0.0km');
            }
          }

          if (needsUpdate) {
            await _storageService.updateTripData(
              trip.id,
              distance: distance,
              fuelConsumed: fuel,
            );
            fixedCount++;
          }
        }
      }

      if (kDebugMode) {
        print('📊 REPORTS: Fixed $fixedCount trips with null values');
      }
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS: Error fixing null trip values: $e');
      }
    }
  }

  // Close storage service
  Future<void> close() async {
    await _storageService.close();
  }
}
