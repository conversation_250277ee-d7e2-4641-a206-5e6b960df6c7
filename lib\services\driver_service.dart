import 'package:flutter/foundation.dart';
import '../models/driver.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import 'api_error.dart';

class DriverService {
  final ApiService _apiService = ApiService();

  // Set access token for authenticated requests
  void setAccessToken(String token) {
    _apiService.setAccessToken(token);
  }

  // Get current user's driver profile
  Future<Driver> getCurrentUserDriver() async {
    if (kDebugMode) {
      print('🚗 DRIVER: Getting current user driver profile');
    }

    try {
      final response = await _apiService.get('/api/drivers/me');

      if (kDebugMode) {
        print('🚗 DRIVER: Driver response: $response');
      }

      final apiResponse = ApiResponse<Driver>.fromJson(
        response,
        (data) => Driver.fromJson(data as Map<String, dynamic>),
      );

      if (apiResponse.success && apiResponse.data != null) {
        return apiResponse.data!;
      } else {
        throw ApiException(ApiError(
          message: 'Failed to get driver profile',
          details: apiResponse.error ?? apiResponse.message,
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('🚗 DRIVER: Error getting driver profile: $e');
      }
      rethrow;
    }
  }
}
