import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'utils/responsive_size.dart';
import 'utils/app_theme.dart';
import 'models/reports_data.dart';
import 'models/driver.dart';
import 'services/reports_service.dart';
import 'services/auth_service.dart';
import 'services/driver_service.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with TickerProviderStateMixin {
  final ResponsiveSize _responsiveSize = ResponsiveSize();
  final ReportsService _reportsService = ReportsService();
  final AuthService _authService = AuthService();
  final DriverService _driverService = DriverService();

  // State variables
  bool _isLoading = true;
  String? _errorMessage;
  ReportsData? _reportsData;
  Driver? _currentDriver;
  ReportPeriod _selectedPeriod = ReportPeriod.monthly;
  final int _selectedTabIndex = 0;

  // Animation controllers
  late TabController _tabController;
  late AnimationController _chartAnimationController;
  late Animation<double> _chartAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadReportsData();
  }

  void _initializeAnimations() {
    _tabController = TabController(length: 3, vsync: this);
    _chartAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _chartAnimation = CurvedAnimation(
      parent: _chartAnimationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _chartAnimationController.dispose();
    _reportsService.close();
    super.dispose();
  }

  Future<void> _loadReportsData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final accessToken = await _authService.getAccessToken();
      if (accessToken == null) {
        throw Exception('No access token found');
      }

      _driverService.setAccessToken(accessToken);
      _reportsService.setAccessToken(accessToken);

      _currentDriver = await _driverService.getCurrentUserDriver();
      _reportsData = await _reportsService.getReportsData(
          _currentDriver!.id, _selectedPeriod);

      _chartAnimationController.forward();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
      debugPrint('📊 REPORTS: Error loading data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    _responsiveSize.init(context);

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppTheme.backgroundLight,
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                // Enhanced Header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: Container(
                        padding: const EdgeInsets.all(AppTheme.spacingSmall),
                        decoration: AppTheme.getCardDecoration(),
                        child: Icon(Icons.arrow_back_ios_new, 
                          color: AppTheme.primaryColor,
                          size: AppTheme.iconSizeSmall,
                        ),
                      ),
                    ),
                    Text(
                      'التقارير',
                      style: AppTheme.getTitleStyle(),
                    ),
                    IconButton(
                      onPressed: _loadReportsData,
                      icon: Container(
                        padding: const EdgeInsets.all(AppTheme.spacingSmall),
                        decoration: AppTheme.getCardDecoration(),
                        child: Icon(Icons.refresh, 
                          color: AppTheme.primaryColor,
                          size: AppTheme.iconSizeSmall,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Expanded(
                  child: _buildBody(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_reportsData == null) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadReportsData,
      color: AppTheme.primaryColor,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            _buildPeriodSelector(),
            const SizedBox(height: 24),
            _buildSummaryCards(),
            const SizedBox(height: 24),
            _buildChartsSection(),
            const SizedBox(height: 24),
            _buildDetailedStats(),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppTheme.primaryColor,
            strokeWidth: 3,
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل التقارير...',
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.textMedium,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.accentRed.withOpacity(0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                Icons.error_outline,
                size: 48,
                color: AppTheme.accentRed,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل التقارير',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textDark,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textMedium,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadReportsData,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 0,
              ),
              child: const Text(
                'إعادة المحاولة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                Icons.assessment_outlined,
                size: 48,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد تقارير متاحة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textDark,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم عرض التقارير هنا عند توفرها',
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.textMedium,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      padding: ResponsiveSize.paddingSymmetric(vertical: 0.5, horizontal: 0.5),
      decoration: BoxDecoration(
        color: AppTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: ReportPeriod.values.map((period) {
          final isSelected = period == _selectedPeriod;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedPeriod = period;
                });
                _loadReportsData();
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: ResponsiveSize.paddingSymmetric(vertical: 2),
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.primaryColor : Colors.transparent,
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Text(
                  period.displayName,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: ResponsiveSize.fontSize(3),
                    color: isSelected ? Colors.white : AppTheme.textMedium,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSummaryCards() {
    final data = _reportsData!;
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: ResponsiveSize.height(1.5),
      crossAxisSpacing: ResponsiveSize.width(1.5),
      childAspectRatio: 1.3,
      children: [
        _buildSummaryCard(
          title: 'إجمالي الرحلات',
          value: data.totalTrips.toString(),
          icon: Icons.route,
          color: AppTheme.primaryColor,
        ),
        _buildSummaryCard(
          title: 'إجمالي المسافة',
          value: '${data.totalDistance.toStringAsFixed(1)} كم',
          icon: Icons.speed,
          color: AppTheme.primaryColor,
        ),
        _buildSummaryCard(
          title: 'إجمالي الوقود',
          value: '${data.totalFuel.toStringAsFixed(1)} لتر',
          icon: Icons.local_gas_station,
          color: AppTheme.primaryColor,
        ),
        _buildSummaryCard(
          title: 'متوسط السرعة',
          value: '${data.avgSpeed.toStringAsFixed(1)} كم/ساعة',
          icon: Icons.speed,
          color: AppTheme.primaryColor,
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: ResponsiveSize.paddingAll(3),
      decoration: BoxDecoration(
        color: AppTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Container(
                padding: ResponsiveSize.paddingAll(1.5),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: ResponsiveSize.width(4),
                ),
              ),
              const Spacer(),
              Icon(
                Icons.trending_up,
                color: AppTheme.accentGreen,
                size: ResponsiveSize.width(3.5),
              ),
            ],
          ),
          SizedBox(height: ResponsiveSize.height(1.5)),
          Text(
            title,
            style: TextStyle(
              fontSize: ResponsiveSize.fontSize(2.8),
              color: AppTheme.textMedium,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: ResponsiveSize.height(0.5)),
          Text(
            value,
            style: TextStyle(
              fontSize: ResponsiveSize.fontSize(3.5),
              color: AppTheme.textDark,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildChartsSection() {
    return Container(
      padding: ResponsiveSize.paddingAll(4),
      decoration: BoxDecoration(
        color: AppTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الرسوم البيانية',
            style: TextStyle(
              fontSize: ResponsiveSize.fontSize(4.5),
              color: AppTheme.textDark,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: ResponsiveSize.height(3)),
          _buildChartCard(
            title: 'المسافة والوقود',
            icon: Icons.route,
            color: AppTheme.primaryColor,
            chart: _buildDistanceFuelChart(),
          ),
          SizedBox(height: ResponsiveSize.height(3)),
          _buildChartCard(
            title: 'الأداء والسلامة',
            icon: Icons.speed,
            color: AppTheme.primaryColor,
            chart: _buildPerformanceChart(),
          ),
        ],
      ),
    );
  }

  Widget _buildChartCard({
    required String title,
    required IconData icon,
    required Color color,
    required Widget chart,
  }) {
    return Container(
      padding: ResponsiveSize.paddingAll(3),
      decoration: BoxDecoration(
        color: AppTheme.backgroundLight,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: ResponsiveSize.paddingAll(1.5),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusSmall),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: ResponsiveSize.width(4),
                ),
              ),
              SizedBox(width: ResponsiveSize.width(2)),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: ResponsiveSize.fontSize(3.5),
                    color: AppTheme.textDark,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: ResponsiveSize.height(2)),
          SizedBox(
            height: ResponsiveSize.height(20),
            child: chart,
          ),
        ],
      ),
    );
  }

  Widget _buildDistanceFuelChart() {
    final data = _reportsData!;
    return LineChart(
      LineChartData(
        gridData: FlGridData(show: false),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (double value, TitleMeta meta) {
                if (value.toInt() >= 0 && value.toInt() < data.dailyData.length) {
                  final date = data.dailyData[value.toInt()].date;
                  return SideTitleWidget(
                    axisSide: meta.axisSide,
                    child: Text(
                      _formatChartLabel(date, _selectedPeriod),
                      style: TextStyle(
                        color: AppTheme.textMedium,
                        fontSize: ResponsiveSize.fontSize(2.5),
                      ),
                    ),
                  );
                }
                return const Text('');
              },
              reservedSize: 30,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 42,
              getTitlesWidget: (double value, TitleMeta meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(
                    color: AppTheme.textMedium,
                    fontSize: ResponsiveSize.fontSize(2.5),
                  ),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: data.dailyData.asMap().entries.map((entry) {
              return FlSpot(entry.key.toDouble(), entry.value.distance);
            }).toList(),
            isCurved: true,
            color: AppTheme.primaryColor,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: AppTheme.primaryColor.withOpacity(0.1),
            ),
          ),
          LineChartBarData(
            spots: data.dailyData.asMap().entries.map((entry) {
              return FlSpot(entry.key.toDouble(), entry.value.fuel * 10);
            }).toList(),
            isCurved: true,
            color: AppTheme.accentYellow,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: AppTheme.accentYellow.withOpacity(0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceChart() {
    final data = _reportsData!;
    return LineChart(
      LineChartData(
        gridData: FlGridData(show: false),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (double value, TitleMeta meta) {
                if (value.toInt() >= 0 && value.toInt() < data.performanceData.length) {
                  final date = data.performanceData[value.toInt()].date;
                  return SideTitleWidget(
                    axisSide: meta.axisSide,
                    child: Text(
                      _formatChartLabel(date, _selectedPeriod),
                      style: TextStyle(
                        color: AppTheme.textMedium,
                        fontSize: ResponsiveSize.fontSize(2.5),
                      ),
                    ),
                  );
                }
                return const Text('');
              },
              reservedSize: 30,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 42,
              getTitlesWidget: (double value, TitleMeta meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(
                    color: AppTheme.textMedium,
                    fontSize: ResponsiveSize.fontSize(2.5),
                  ),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: data.performanceData.asMap().entries.map((entry) {
              return FlSpot(entry.key.toDouble(), entry.value.safetyScore);
            }).toList(),
            isCurved: true,
            color: AppTheme.accentGreen,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: AppTheme.accentGreen.withOpacity(0.1),
            ),
          ),
          LineChartBarData(
            spots: data.performanceData.asMap().entries.map((entry) {
              return FlSpot(entry.key.toDouble(), entry.value.avgSpeed);
            }).toList(),
            isCurved: true,
            color: AppTheme.accentBlue,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: AppTheme.accentBlue.withOpacity(0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedStats() {
    final data = _reportsData!;
    return Container(
      padding: ResponsiveSize.paddingAll(3),
      decoration: BoxDecoration(
        color: AppTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: AppTheme.primaryColor,
                size: ResponsiveSize.width(5),
              ),
              SizedBox(width: ResponsiveSize.width(2)),
              Text(
                'إحصائيات مفصلة',
                style: TextStyle(
                  fontSize: ResponsiveSize.fontSize(4),
                  color: AppTheme.textDark,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: ResponsiveSize.height(2)),
          _buildStatRow('متوسط المسافة للرحلة', '${data.avgTripDistance.toStringAsFixed(1)} كم'),
          _buildStatRow('متوسط مدة الرحلة', '${data.avgTripDuration.inMinutes} دقيقة'),
          _buildStatRow('متوسط السرعة', '${data.avgSpeed.toStringAsFixed(1)} كم/ساعة'),
          _buildStatRow('كفاءة استهلاك الوقود', '${data.avgFuelEfficiency.toStringAsFixed(1)} كم/لتر'),
          _buildStatRow('تقييم كفاءة الوقود', data.fuelEfficiencyGrade),
          _buildStatRow('تقييم الأمان', data.safetyGrade),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: ResponsiveSize.paddingSymmetric(vertical: 1.5),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: ResponsiveSize.fontSize(3),
                color: AppTheme.textMedium,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: ResponsiveSize.width(2)),
          Text(
            value,
            style: TextStyle(
              fontSize: ResponsiveSize.fontSize(3),
              color: AppTheme.textDark,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  String _formatChartLabel(DateTime date, ReportPeriod period) {
    switch (period) {
      case ReportPeriod.daily:
        return '${date.hour.toString().padLeft(2, '0')}:00';
      case ReportPeriod.weekly:
        return '${date.day}/${date.month}';
      case ReportPeriod.monthly:
        final weekOfMonth = ((date.day - 1) ~/ 7) + 1;
        return 'أسبوع $weekOfMonth';
      case ReportPeriod.yearly:
        const monthNames = [
          'يناير',
          'فبراير',
          'مارس',
          'أبريل',
          'مايو',
          'يونيو',
          'يوليو',
          'أغسطس',
          'سبتمبر',
          'أكتوبر',
          'نوفمبر',
          'ديسمبر'
        ];
        return monthNames[date.month - 1];
    }
  }
}
