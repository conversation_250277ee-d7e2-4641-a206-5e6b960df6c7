import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';

class LocationService {
  // Calculate distance between two points using Haversine formula
  static double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    // Convert degrees to radians
    double lat1Rad = startLatitude * (pi / 180);
    double lon1Rad = startLongitude * (pi / 180);
    double lat2Rad = endLatitude * (pi / 180);
    double lon2Rad = endLongitude * (pi / 180);

    // Calculate differences
    double deltaLat = lat2Rad - lat1Rad;
    double deltaLon = lon2Rad - lon1Rad;

    // Haversine formula
    double a = sin(deltaLat / 2) * sin(deltaLat / 2) +
        cos(lat1Rad) * cos(lat2Rad) * sin(deltaLon / 2) * sin(deltaLon / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    // Distance in kilometers
    double distance = earthRadius * c;

    if (kDebugMode) {
      print(
          '📍 LOCATION: Calculated distance: ${distance.toStringAsFixed(2)} km');
    }

    return distance;
  }

  // Get address from coordinates using reverse geocoding
  static Future<String> getAddressFromCoordinates(
    double latitude,
    double longitude,
  ) async {
    try {
      if (kDebugMode) {
        print('📍 LOCATION: Getting address for: $latitude, $longitude');
      }

      // Using Nominatim (OpenStreetMap) reverse geocoding service
      final url = Uri.parse(
        'https://nominatim.openstreetmap.org/reverse?format=json&lat=$latitude&lon=$longitude&zoom=18&addressdetails=1',
      );

      final response = await http.get(
        url,
        headers: {
          'User-Agent': 'Drivety-App/1.0',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['display_name'] != null) {
          String address = data['display_name'];

          // Try to extract a more readable address with Arabic only
          if (data['address'] != null) {
            final addressComponents = data['address'];
            List<String> addressParts = [];

            // Add road/street name (Arabic only)
            String? streetName;
            if (addressComponents['road'] != null) {
              streetName = _extractArabicText(addressComponents['road']);
            } else if (addressComponents['pedestrian'] != null) {
              streetName = _extractArabicText(addressComponents['pedestrian']);
            } else if (addressComponents['footway'] != null) {
              streetName = _extractArabicText(addressComponents['footway']);
            }

            if (streetName != null && streetName.isNotEmpty) {
              addressParts.add(streetName);
            }

            // Add city (Arabic only)
            String? cityName;
            if (addressComponents['city'] != null) {
              cityName = _extractArabicText(addressComponents['city']);
            } else if (addressComponents['town'] != null) {
              cityName = _extractArabicText(addressComponents['town']);
            } else if (addressComponents['village'] != null) {
              cityName = _extractArabicText(addressComponents['village']);
            } else if (addressComponents['state'] != null) {
              cityName = _extractArabicText(addressComponents['state']);
            }

            if (cityName != null && cityName.isNotEmpty) {
              addressParts.add(cityName);
            }

            if (addressParts.isNotEmpty) {
              address = addressParts.join('، '); // Arabic comma
            } else {
              // Fallback: try to extract Arabic from display_name
              address =
                  _extractArabicText(data['display_name']) ?? 'موقع غير محدد';
            }
          }

          if (kDebugMode) {
            print('📍 LOCATION: Found Arabic address: $address');
          }

          return address;
        }
      }

      // Fallback to coordinates if address not found
      return 'Lat: ${latitude.toStringAsFixed(6)}, Lng: ${longitude.toStringAsFixed(6)}';
    } catch (e) {
      if (kDebugMode) {
        print('📍 LOCATION: Error getting address: $e');
      }

      // Return coordinates as fallback
      return 'Lat: ${latitude.toStringAsFixed(6)}, Lng: ${longitude.toStringAsFixed(6)}';
    }
  }

  // Get current location
  static Future<Position> getCurrentLocation() async {
    try {
      if (kDebugMode) {
        print('📍 LOCATION: Getting current location...');
      }

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled');
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      if (kDebugMode) {
        print(
            '📍 LOCATION: Current position: ${position.latitude}, ${position.longitude}');
      }

      return position;
    } catch (e) {
      if (kDebugMode) {
        print('📍 LOCATION: Error getting current location: $e');
      }
      rethrow;
    }
  }

  // Get current address
  static Future<String> getCurrentAddress() async {
    try {
      final position = await getCurrentLocation();
      return await getAddressFromCoordinates(
        position.latitude,
        position.longitude,
      );
    } catch (e) {
      if (kDebugMode) {
        print('📍 LOCATION: Error getting current address: $e');
      }
      rethrow;
    }
  }

  // Check if coordinates are valid
  static bool isValidCoordinates(double? latitude, double? longitude) {
    if (latitude == null || longitude == null) return false;

    return latitude >= -90 &&
        latitude <= 90 &&
        longitude >= -180 &&
        longitude <= 180;
  }

  // Format coordinates for display
  static String formatCoordinates(double latitude, double longitude) {
    return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
  }

  // Calculate fuel consumption based on distance (simplified calculation)
  static double calculateFuelConsumption(double distanceKm) {
    // For now, always return 50 as requested
    // In a real app, this could be based on vehicle efficiency, driving conditions, etc.
    const double baseFuelConsumption = 50.0;

    if (kDebugMode) {
      print(
          '📍 LOCATION: Calculated fuel consumption: $baseFuelConsumption liters for ${distanceKm.toStringAsFixed(2)} km');
    }

    return baseFuelConsumption;
  }

  // Get route distance using OpenRouteService (alternative to Google Maps)
  static Future<double?> getRouteDistance(
    double startLat,
    double startLng,
    double endLat,
    double endLng,
  ) async {
    try {
      // For now, use straight-line distance calculation
      // In production, you could integrate with routing services like:
      // - OpenRouteService
      // - MapBox Directions API
      // - Google Directions API

      double straightLineDistance =
          calculateDistance(startLat, startLng, endLat, endLng);

      // Add some factor for actual road distance (typically 1.2-1.5x straight line)
      double routeDistance = straightLineDistance * 1.3;

      if (kDebugMode) {
        print(
            '📍 LOCATION: Estimated route distance: ${routeDistance.toStringAsFixed(2)} km');
      }

      return routeDistance;
    } catch (e) {
      if (kDebugMode) {
        print('📍 LOCATION: Error calculating route distance: $e');
      }
      return null;
    }
  }

  // Extract Arabic text from mixed language strings
  static String? _extractArabicText(String text) {
    if (text.isEmpty) return null;

    // Split by common separators used in OpenStreetMap
    List<String> parts = text.split(RegExp(r'[,،]|\s+ⴱⴻⵏ\s+|\s+ⵍⵣⵣⴰⵢⴻⵔ\s+'));

    List<String> arabicParts = [];

    for (String part in parts) {
      part = part.trim();
      if (part.isEmpty) continue;

      // Check if the part contains Arabic characters
      if (_containsArabic(part)) {
        // Remove any remaining non-Arabic characters but keep Arabic text
        String cleanedPart = _cleanArabicText(part);
        if (cleanedPart.isNotEmpty) {
          arabicParts.add(cleanedPart);
        }
      }
    }

    if (arabicParts.isNotEmpty) {
      // Return the first Arabic part (usually the most relevant)
      return arabicParts.first;
    }

    return null;
  }

  // Check if text contains Arabic characters
  static bool _containsArabic(String text) {
    // Arabic Unicode range: U+0600 to U+06FF
    // Arabic Supplement: U+0750 to U+077F
    // Arabic Extended-A: U+08A0 to U+08FF
    final arabicRegex = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]');
    return arabicRegex.hasMatch(text);
  }

  // Clean Arabic text by removing non-Arabic characters
  static String _cleanArabicText(String text) {
    // Keep Arabic characters, spaces, and common punctuation
    final arabicRegex =
        RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\s\-\(\)]+');
    final matches = arabicRegex.allMatches(text);

    String cleaned = '';
    for (final match in matches) {
      cleaned += match.group(0) ?? '';
    }

    // Clean up extra spaces and trim
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ').trim();

    // Remove any remaining parentheses or dashes at the beginning/end
    cleaned = cleaned.replaceAll(RegExp(r'^[\-\(\)\s]+|[\-\(\)\s]+$'), '');

    return cleaned;
  }
}
