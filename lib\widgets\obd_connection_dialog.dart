import 'package:flutter/material.dart';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import '../utils/responsive_size.dart';
import '../utils/app_theme.dart';
import '../services/obd_service.dart';
import '../services/bluetooth_permissions.dart';
import 'bluetooth_permission_dialog.dart';

class OBDConnectionDialog extends StatefulWidget {
  const OBDConnectionDialog({super.key});

  @override
  State<OBDConnectionDialog> createState() => _OBDConnectionDialogState();
}

class _OBDConnectionDialogState extends State<OBDConnectionDialog>
    with TickerProviderStateMixin {
  List<BluetoothDevice> _devices = [];
  BluetoothDevice? _selectedDevice;
  bool _isScanning = false;
  bool _isConnecting = false;
  String _connectionStatus = '';
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
    _checkPermissionsAndLoadDevices();
  }

  Future<void> _checkPermissionsAndLoadDevices() async {
    try {
      setState(() {
        _isScanning = true;
        _connectionStatus = 'فحص أذونات البلوتوث...';
      });

      // Check Bluetooth permissions
      final permissionResult = await BluetoothPermissions.checkPermissions();

      if (permissionResult.status != BluetoothPermissionStatus.granted) {
        setState(() {
          _isScanning = false;
          _connectionStatus = permissionResult.message;
        });

        // Show permission dialog
        if (mounted) {
          final granted = await showBluetoothPermissionDialog(context);
          if (granted == true) {
            // Permissions granted, try loading devices again
            _loadBondedDevices();
          } else {
            // Permissions denied, close this dialog
            if (mounted) {
              Navigator.of(context).pop(false);
            }
          }
        }
        return;
      }

      // Permissions are granted, load devices
      _loadBondedDevices();
    } catch (e) {
      setState(() {
        _isScanning = false;
        _connectionStatus = 'خطأ في فحص الأذونات: ${e.toString()}';
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadBondedDevices() async {
    try {
      setState(() {
        _isScanning = true;
        _connectionStatus = 'البحث عن الأجهزة المقترنة...';
      });

      final devices = await FlutterBluetoothSerial.instance.getBondedDevices();
      setState(() {
        _devices = devices.where((device) => 
          device.name?.toLowerCase().contains('obd') == true ||
          device.name?.toLowerCase().contains('elm') == true ||
          device.name?.toLowerCase().contains('327') == true
        ).toList();
        _isScanning = false;
        _connectionStatus = _devices.isEmpty 
          ? 'لم يتم العثور على أجهزة OBD مقترنة'
          : 'تم العثور على ${_devices.length} جهاز';
      });
    } catch (e) {
      setState(() {
        _isScanning = false;
        _connectionStatus = 'خطأ في البحث عن الأجهزة: ${e.toString()}';
      });
    }
  }

  Future<void> _connectToDevice(BluetoothDevice device) async {
    setState(() {
      _isConnecting = true;
      _selectedDevice = device;
      _connectionStatus = 'جاري الاتصال بـ ${device.name}...';
    });

    try {
      await OBDService.connectToOBD();
      setState(() {
        _isConnecting = false;
        _connectionStatus = 'تم الاتصال بنجاح!';
      });
      
      // Close dialog after successful connection
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      setState(() {
        _isConnecting = false;
        _connectionStatus = 'فشل الاتصال: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Initialize ResponsiveSize if not already initialized
    if (!ResponsiveSize.isInitialized) {
      ResponsiveSize().init(context);
    }

    return ScaleTransition(
      scale: _scaleAnimation,
      child: Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          width: MediaQuery.of(context).size.width * (ResponsiveSize.isTablet ? 0.6 : 0.9),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.75,
            minHeight: MediaQuery.of(context).size.height * 0.35,
            maxWidth: ResponsiveSize.isTablet ? 600 : double.infinity,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(ResponsiveSize.isTablet ? 20 : 16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              Flexible(
                child: _buildContent(),
              ),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final borderRadius = ResponsiveSize.isTablet ? 20.0 : 16.0;
    return Container(
      padding: EdgeInsets.all(ResponsiveSize.isTablet ? 20 : 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(borderRadius),
          topRight: Radius.circular(borderRadius),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(ResponsiveSize.isTablet ? 12 : 10),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(ResponsiveSize.isTablet ? 12 : 10),
            ),
            child: Icon(
              Icons.bluetooth,
              color: Colors.white,
              size: ResponsiveSize.isTablet ? 24 : 20,
            ),
          ),
          SizedBox(width: ResponsiveSize.isTablet ? 16 : 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'اتصال OBD-II',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: ResponsiveSize.isTablet ? 20 : 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'اختر جهاز OBD للاتصال',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: ResponsiveSize.isTablet ? 14 : 13,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(false),
            icon: Icon(
              Icons.close,
              color: Colors.white,
              size: ResponsiveSize.isTablet ? 24 : 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: EdgeInsets.all(ResponsiveSize.isTablet ? 20 : 16),
      child: Column(
        children: [
          // Status indicator
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(ResponsiveSize.isTablet ? 16 : 12),
            decoration: BoxDecoration(
              color: AppTheme.backgroundLight,
              borderRadius: BorderRadius.circular(ResponsiveSize.isTablet ? 12 : 10),
              border: Border.all(
                color: Colors.grey.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                if (_isScanning || _isConnecting)
                  SizedBox(
                    width: ResponsiveSize.isTablet ? 20 : 16,
                    height: ResponsiveSize.isTablet ? 20 : 16,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppTheme.primaryColor,
                      ),
                    ),
                  )
                else
                  Icon(
                    _devices.isEmpty ? Icons.warning : Icons.info,
                    color: _devices.isEmpty ? AppTheme.accentRed : AppTheme.primaryColor,
                    size: ResponsiveSize.isTablet ? 20 : 16,
                  ),
                SizedBox(width: ResponsiveSize.isTablet ? 12 : 8),
                Expanded(
                  child: Text(
                    _connectionStatus,
                    style: TextStyle(
                      fontSize: ResponsiveSize.isTablet ? 14 : 13,
                      color: AppTheme.textMedium,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: ResponsiveSize.height(2)),
          
          // Device list
          if (_devices.isNotEmpty)
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _devices.length,
                itemBuilder: (context, index) {
                  final device = _devices[index];
                  final isSelected = _selectedDevice?.address == device.address;
                  
                  return Container(
                    margin: EdgeInsets.only(bottom: ResponsiveSize.height(1)),
                    decoration: BoxDecoration(
                      color: isSelected 
                        ? AppTheme.primaryColor.withValues(alpha: 0.1)
                        : Colors.white,
                      borderRadius: BorderRadius.circular(ResponsiveSize.width(2)),
                      border: Border.all(
                        color: isSelected 
                          ? AppTheme.primaryColor
                          : Colors.grey.withValues(alpha: 0.2),
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: ListTile(
                      leading: Container(
                        padding: EdgeInsets.all(ResponsiveSize.isTablet ? 10 : 8),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(ResponsiveSize.isTablet ? 10 : 8),
                        ),
                        child: Icon(
                          Icons.car_repair,
                          color: AppTheme.primaryColor,
                          size: ResponsiveSize.isTablet ? 20 : 18,
                        ),
                      ),
                      title: Text(
                        device.name ?? 'جهاز غير معروف',
                        style: TextStyle(
                          fontSize: ResponsiveSize.isTablet ? 16 : 15,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.textDark,
                        ),
                      ),
                      subtitle: Text(
                        device.address,
                        style: TextStyle(
                          fontSize: ResponsiveSize.isTablet ? 12 : 11,
                          color: AppTheme.textMedium,
                        ),
                      ),
                      trailing: _isConnecting && isSelected
                        ? SizedBox(
                            width: ResponsiveSize.isTablet ? 20 : 18,
                            height: ResponsiveSize.isTablet ? 20 : 18,
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppTheme.primaryColor,
                              ),
                            ),
                          )
                        : Icon(
                            isSelected ? Icons.check_circle : Icons.radio_button_unchecked,
                            color: isSelected ? AppTheme.primaryColor : AppTheme.textMedium,
                            size: ResponsiveSize.isTablet ? 20 : 18,
                          ),
                      onTap: _isConnecting ? null : () => _connectToDevice(device),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    final borderRadius = ResponsiveSize.isTablet ? 20.0 : 16.0;
    return Container(
      padding: EdgeInsets.all(ResponsiveSize.isTablet ? 20 : 16),
      decoration: BoxDecoration(
        color: AppTheme.backgroundLight,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(borderRadius),
          bottomRight: Radius.circular(borderRadius),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextButton.icon(
              onPressed: _isScanning ? null : _checkPermissionsAndLoadDevices,
              icon: Icon(
                Icons.refresh,
                size: ResponsiveSize.isTablet ? 18 : 16,
              ),
              label: Text(
                'إعادة البحث',
                style: TextStyle(
                  fontSize: ResponsiveSize.isTablet ? 14 : 13,
                ),
              ),
            ),
          ),
          SizedBox(width: ResponsiveSize.isTablet ? 12 : 8),
          Expanded(
            child: ElevatedButton(
              onPressed: () => Navigator.of(context).pop(false),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.textMedium,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  vertical: ResponsiveSize.isTablet ? 14 : 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(ResponsiveSize.isTablet ? 12 : 10),
                ),
              ),
              child: Text(
                'إلغاء',
                style: TextStyle(
                  fontSize: ResponsiveSize.isTablet ? 14 : 13,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Helper function to show the OBD connection dialog
Future<bool?> showOBDConnectionDialog(BuildContext context) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => const OBDConnectionDialog(),
  );
}
