import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'splash_screen.dart';
import 'utils/app_theme.dart';
import 'services/bluetooth_permissions.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'homep_fixed.dart';
import 'loginscreen.dart';
import 'trips_screen.dart';
import 'trip_management_screen.dart';
import 'settings_page.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  
  // Request Bluetooth permissions only on mobile platforms
  if (!kIsWeb) {
    await BluetoothPermissions.requestPermissions();
  }
  
  runApp(const DrivetyApp());
}

class DrivetyApp extends StatelessWidget {
  const DrivetyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Drivety',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.getLightTheme(),
      locale: const Locale('ar'), // Arabic as default
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar'),
        Locale('en'),
      ],
      home: const SplashScreen(),
      routes: {
        '/home': (context) => const HomePage(),
        '/login': (context) => const LoginScreen(),
        '/trips': (context) => const TripsScreen(),
        '/trip-management': (context) => const TripManagementScreen(),
        '/settings': (context) => const SettingsPage(),
      },
    );
  }
}
