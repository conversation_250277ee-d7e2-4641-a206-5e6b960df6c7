import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'splash_screen.dart';
import 'utils/app_theme.dart';
import 'services/bluetooth_permissions.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  
  // Request Bluetooth permissions only on mobile platforms
  if (!kIsWeb) {
    await BluetoothPermissions.requestPermissions();
  }
  
  runApp(const DrivetyApp());
}

class DrivetyApp extends StatelessWidget {
  const DrivetyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Drivety',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.getLightTheme(),
      locale: const Locale('ar'), // Arabic as default
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('ar'),
        Locale('en'),
      ],
      home: const SplashScreen(),
    );
  }
}
