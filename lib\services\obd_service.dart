import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;

class OBDService {
  static BluetoothConnection? connection;
  static final FlutterBluetoothSerial bluetooth = FlutterBluetoothSerial.instance;

  static Future<void> connectToOBD() async {
    if (kIsWeb) {
      print('Bluetooth is not supported on web platform');
      return;
    }

    try {
      // تأكد من تفعيل البلوتوث
      await bluetooth.requestEnable();

      // البحث عن الأجهزة المقترنة
      List<BluetoothDevice> devices = await bluetooth.getBondedDevices();

      // اختيار جهاز OBD (عادة اسمه يكون "OBDII" أو شيء مشابه)
      BluetoothDevice? obdDevice = devices.firstWhere(
        (device) => device.name?.contains("OBD") ?? false,
        orElse: () => throw Exception("OBD-II device not found"),
      );

      // الاتصال بالجهاز
      connection = await BluetoothConnection.toAddress(obdDevice.address);
      print('Connected to OBD device');
      
      // Start listening for data after connection
      listenForData();
    } catch (e) {
      print('Error connecting to OBD device: $e');
      rethrow;
    }
  }

  static Future<void> disconnect() async {
    if (kIsWeb) {
      print('Bluetooth is not supported on web platform');
      return;
    }

    if (connection != null) {
      await connection!.close();
      connection = null;
      print('Disconnected from OBD device');
    }
  }

  static bool isConnected() {
    if (kIsWeb) {
      return false;
    }
    return connection != null && connection!.isConnected;
  }

  static void sendOBDCommand(String command) {
    if (kIsWeb) {
      print('Bluetooth is not supported on web platform');
      return;
    }

    if (connection != null && connection!.isConnected) {
      connection!.output.add(Uint8List.fromList(utf8.encode("$command\r")));
      connection!.output.allSent;
    }
  }

  static void listenForData() {
    if (kIsWeb) {
      print('Bluetooth is not supported on web platform');
      return;
    }

    connection?.input?.listen((Uint8List data) {
      print('Data incoming: ${ascii.decode(data)}');
    });
  }
} 