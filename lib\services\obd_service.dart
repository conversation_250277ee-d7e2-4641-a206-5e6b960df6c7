import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'dart:convert';
import 'dart:typed_data';
import 'bluetooth_permissions.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class OBDService {
  static BluetoothConnection? connection;
  static final FlutterBluetoothSerial bluetooth = FlutterBluetoothSerial.instance;

  static Future<void> connectToOBD() async {
    if (kIsWeb) {
      print('Bluetooth is not supported on web platform');
      return;
    }

    try {
      // تأكد من تفعيل البلوتوث
      await bluetooth.requestEnable();

      // Use enhanced device discovery to get all available devices
      List<BluetoothDevice> allDevices = await BluetoothPermissions.getAllBluetoothDevices(
        includeBonded: true,
        includeDiscovered: true,
        scanTimeout: const Duration(seconds: 10),
      );

      print('🔍 OBD SERVICE: Found ${allDevices.length} total devices');
      for (BluetoothDevice device in allDevices) {
        print('🔍 OBD SERVICE: Device: ${device.name ?? "Unknown"} (${device.address})');
      }

      // Look for any device that might be an OBD device (flexible matching)
      BluetoothDevice? obdDevice;

      // First try to find devices with OBD-related names
      for (BluetoothDevice device in allDevices) {
        if (BluetoothPermissions.isLikelyOBDDevice(device)) {
          obdDevice = device;
          print('🔍 OBD SERVICE: Found likely OBD device: ${device.name} (${device.address})');
          break;
        }
      }

      // If no OBD-like device found, allow user to connect to any device for testing
      if (obdDevice == null && allDevices.isNotEmpty) {
        obdDevice = allDevices.first;
        print('🔍 OBD SERVICE: No OBD device found, using first available device for testing: ${obdDevice.name} (${obdDevice.address})');
      }

      if (obdDevice == null) {
        throw Exception("No Bluetooth devices found. Make sure devices are discoverable and try again.");
      }

      print('🔗 OBD SERVICE: Attempting to connect to: ${obdDevice.name} (${obdDevice.address})');

      // الاتصال بالجهاز
      connection = await BluetoothConnection.toAddress(obdDevice.address);
      print('✅ OBD SERVICE: Connected to device: ${obdDevice.name}');

      // Start listening for data after connection
      listenForData();
    } catch (e) {
      print('❌ OBD SERVICE: Error connecting to device');
      rethrow;
    }
  }

  /// Connect to a specific Bluetooth device
  static Future<void> connectToDevice(BluetoothDevice device) async {
    if (kIsWeb) {
      print('Bluetooth is not supported on web platform');
      return;
    }

    try {
      print('🔗 OBD SERVICE: Attempting to connect to: ${device.name} (${device.address})');

      // الاتصال بالجهاز
      connection = await BluetoothConnection.toAddress(device.address);
      print('✅ OBD SERVICE: Connected to device: ${device.name}');

      // Start listening for data after connection
      listenForData();
    } catch (e) {
      print('❌ OBD SERVICE: Error connecting to device');
      rethrow;
    }
  }

  static Future<void> disconnect() async {
    if (kIsWeb) {
      print('Bluetooth is not supported on web platform');
      return;
    }

    if (connection != null) {
      await connection!.close();
      connection = null;
      print('Disconnected from OBD device');
    }
  }

  static bool isConnected() {
    if (kIsWeb) {
      return false;
    }
    return connection != null && connection!.isConnected;
  }

  static void sendOBDCommand(String command) {
    if (kIsWeb) {
      print('Bluetooth is not supported on web platform');
      return;
    }

    if (connection != null && connection!.isConnected) {
      connection!.output.add(Uint8List.fromList(utf8.encode("$command\r")));
      connection!.output.allSent;
    }
  }

  static void listenForData() {
    if (kIsWeb) {
      print('Bluetooth is not supported on web platform');
      return;
    }

    connection?.input?.listen((Uint8List data) {
      print('Data incoming: ${ascii.decode(data)}');
    });
  }
} 