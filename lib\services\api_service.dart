import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'api_error.dart';

class ApiService {
  // Try multiple URLs in order if one fails
  static const List<String> _baseUrls = [
    'https://ecd8-105-108-220-16.ngrok-free.app',
    'http://localhost:3000', // fallback to local development server
    'http://127.0.0.1:3000', // alternative localhost
  ];
  
  static String? _workingBaseUrl;
  static const Duration timeoutDuration = Duration(seconds: 30);

  String? _accessToken;

  // Get working base URL (try to find one that works)
  Future<String> _getBaseUrl() async {
    if (_workingBaseUrl != null) {
      return _workingBaseUrl!;
    }
    
    // Try each URL to see which one works
    for (String url in _baseUrls) {
      try {
        final testUrl = Uri.parse('$url/api/health'); // Try a health check endpoint
        final response = await http.get(testUrl).timeout(const Duration(seconds: 5));
        if (response.statusCode < 500) { // Accept any non-server error
          _workingBaseUrl = url;
          if (kDebugMode) {
            print('✅ Using base URL: $url');
          }
          return url;
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Base URL $url failed: $e');
        }
        continue;
      }
    }
    
    // If no URL works, use the first one as fallback
    _workingBaseUrl = _baseUrls.first;
    if (kDebugMode) {
      print('⚠️ Using fallback base URL: ${_baseUrls.first}');
    }
    return _baseUrls.first;
  }

  // Set access token for authenticated requests
  void setAccessToken(String token) {
    _accessToken = token;
  }

  // Clear access token
  void clearAccessToken() {
    _accessToken = null;
  }

  // Log request details
  void _logRequest(String method, String url, Map<String, String> headers,
      [String? body]) {
    if (kDebugMode) {
      print('🚀 API REQUEST');
      print('📍 Method: $method');
      print('🌐 URL: $url');
      print('📋 Headers: $headers');
      if (body != null) {
        print('📦 Body: $body');
      }
      print('⏰ Time: ${DateTime.now()}');
      print('─' * 50);
    }
  }

  // Log response details
  void _logResponse(String method, String url, http.Response response) {
    if (kDebugMode) {
      print('📥 API RESPONSE');
      print('📍 Method: $method');
      print('🌐 URL: $url');
      print('📊 Status Code: ${response.statusCode}');
      print('📋 Headers: ${response.headers}');
      print('📦 Body: ${response.body}');
      print('⏰ Time: ${DateTime.now()}');
      print('─' * 50);
    }
  }

  // Log error details
  void _logError(String method, String url, dynamic error) {
    if (kDebugMode) {
      print('❌ API ERROR');
      print('📍 Method: $method');
      print('🌐 URL: $url');
      print('💥 Error: $error');
      print('⏰ Time: ${DateTime.now()}');
      print('─' * 50);
    }
  }

  // Get headers without access token (for auth endpoints)
  Map<String, String> _getAuthHeaders() {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'ngrok-skip-browser-warning': 'true', // Skip ngrok browser warning
    };
  }

  // Get headers with access token (for other endpoints)
  Map<String, String> _getHeaders() {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'ngrok-skip-browser-warning': 'true', // Skip ngrok browser warning
    };

    if (_accessToken != null) {
      headers['Authorization'] = 'Bearer $_accessToken';
    }

    return headers;
  }

  // Handle response and errors
  dynamic _handleResponse(String method, String url, http.Response response) {
    // Log the response
    _logResponse(method, url, response);

    Map<String, dynamic>? responseData;

    try {
      if (response.body.isNotEmpty) {
        responseData = json.decode(response.body);
      }
    } catch (e) {
      _logError(method, url, 'JSON decode error: $e');
      // If response is not valid JSON, treat as error
      if (response.statusCode >= 400) {
        throw ApiException(ApiError.fromResponse(response.statusCode, null));
      }
    }

    if (response.statusCode >= 200 && response.statusCode < 300) {
      return responseData;
    } else {
      throw ApiException(
          ApiError.fromResponse(response.statusCode, responseData));
    }
  }

  // Helper to join base URL and endpoint safely
  String _joinUrl(String base, String endpoint) {
    if (base.endsWith('/')) base = base.substring(0, base.length - 1);
    if (endpoint.startsWith('/')) endpoint = endpoint.substring(1);
    return '$base/$endpoint';
  }

  // POST method for authentication (without access token)
  Future<dynamic> postAuth(String endpoint, Map<String, dynamic> data) async {
    final baseUrl = await _getBaseUrl();
    final url = Uri.parse(_joinUrl(baseUrl, endpoint));
    final headers = _getAuthHeaders();
    final body = json.encode(data);

    try {
      // Log the request
      _logRequest('POST', url.toString(), headers, body);

      final response = await http
          .post(
            url,
            headers: headers,
            body: body,
          )
          .timeout(timeoutDuration);

      return _handleResponse('POST', url.toString(), response);
    } on SocketException catch (e) {
      _logError('POST', url.toString(), 'SocketException: $e');
      throw ApiException(ApiError.networkError());
    } on http.ClientException catch (e) {
      _logError('POST', url.toString(), 'ClientException: $e');
      throw ApiException(ApiError.timeoutError());
    } catch (e) {
      _logError('POST', url.toString(), 'Unknown error: $e');
      if (e is ApiException) rethrow;
      throw ApiException(ApiError.unknownError());
    }
  }

  // POST method with access token (for other operations)
  Future<dynamic> post(String endpoint, Map<String, dynamic> data) async {
    final baseUrl = await _getBaseUrl();
    final url = Uri.parse(_joinUrl(baseUrl, endpoint));
    final headers = _getHeaders();
    final body = json.encode(data);

    try {
      // Log the request
      _logRequest('POST', url.toString(), headers, body);

      final response = await http
          .post(
            url,
            headers: headers,
            body: body,
          )
          .timeout(timeoutDuration);

      return _handleResponse('POST', url.toString(), response);
    } on SocketException catch (e) {
      _logError('POST', url.toString(), 'SocketException: $e');
      throw ApiException(ApiError.networkError());
    } on http.ClientException catch (e) {
      _logError('POST', url.toString(), 'ClientException: $e');
      throw ApiException(ApiError.timeoutError());
    } catch (e) {
      _logError('POST', url.toString(), 'Unknown error: $e');
      if (e is ApiException) rethrow;
      throw ApiException(ApiError.unknownError());
    }
  }

  // GET method
  Future<dynamic> get(String endpoint,
      {Map<String, String>? queryParams}) async {
    final baseUrl = await _getBaseUrl();
    Uri url = Uri.parse(_joinUrl(baseUrl, endpoint));
    if (queryParams != null && queryParams.isNotEmpty) {
      url = url.replace(queryParameters: queryParams);
    }
    final headers = _getHeaders();

    try {
      // Log the request
      _logRequest('GET', url.toString(), headers);

      final response = await http
          .get(
            url,
            headers: headers,
          )
          .timeout(timeoutDuration);

      return _handleResponse('GET', url.toString(), response);
    } on SocketException catch (e) {
      _logError('GET', url.toString(), 'SocketException: $e');
      throw ApiException(ApiError.networkError());
    } on http.ClientException catch (e) {
      _logError('GET', url.toString(), 'ClientException: $e');
      throw ApiException(ApiError.timeoutError());
    } catch (e) {
      _logError('GET', url.toString(), 'Unknown error: $e');
      if (e is ApiException) rethrow;
      throw ApiException(ApiError.unknownError());
    }
  }

  // PUT method
  Future<dynamic> put(String endpoint, Map<String, dynamic> data) async {
    final baseUrl = await _getBaseUrl();
    final url = Uri.parse(_joinUrl(baseUrl, endpoint));
    final headers = _getHeaders();
    final body = json.encode(data);

    try {
      // Log the request
      _logRequest('PUT', url.toString(), headers, body);

      final response = await http
          .put(
            url,
            headers: headers,
            body: body,
          )
          .timeout(timeoutDuration);

      return _handleResponse('PUT', url.toString(), response);
    } on SocketException catch (e) {
      _logError('PUT', url.toString(), 'SocketException: $e');
      throw ApiException(ApiError.networkError());
    } on http.ClientException catch (e) {
      _logError('PUT', url.toString(), 'ClientException: $e');
      throw ApiException(ApiError.timeoutError());
    } catch (e) {
      _logError('PUT', url.toString(), 'Unknown error: $e');
      if (e is ApiException) rethrow;
      throw ApiException(ApiError.unknownError());
    }
  }

  // PATCH method
  Future<dynamic> patch(String endpoint, Map<String, dynamic> data) async {
    final baseUrl = await _getBaseUrl();
    final url = Uri.parse(_joinUrl(baseUrl, endpoint));
    final headers = _getHeaders();
    final body = json.encode(data);

    try {
      // Log the request
      _logRequest('PATCH', url.toString(), headers, body);

      final response = await http
          .patch(
            url,
            headers: headers,
            body: body,
          )
          .timeout(timeoutDuration);

      return _handleResponse('PATCH', url.toString(), response);
    } on SocketException catch (e) {
      _logError('PATCH', url.toString(), 'SocketException: $e');
      throw ApiException(ApiError.networkError());
    } on http.ClientException catch (e) {
      _logError('PATCH', url.toString(), 'ClientException: $e');
      throw ApiException(ApiError.timeoutError());
    } catch (e) {
      _logError('PATCH', url.toString(), 'Unknown error: $e');
      if (e is ApiException) rethrow;
      throw ApiException(ApiError.unknownError());
    }
  }

  // DELETE method
  Future<dynamic> delete(String endpoint) async {
    final baseUrl = await _getBaseUrl();
    final url = Uri.parse(_joinUrl(baseUrl, endpoint));
    final headers = _getHeaders();

    try {
      // Log the request
      _logRequest('DELETE', url.toString(), headers);

      final response = await http
          .delete(
            url,
            headers: headers,
          )
          .timeout(timeoutDuration);

      return _handleResponse('DELETE', url.toString(), response);
    } on SocketException catch (e) {
      _logError('DELETE', url.toString(), 'SocketException: $e');
      throw ApiException(ApiError.networkError());
    } on http.ClientException catch (e) {
      _logError('DELETE', url.toString(), 'ClientException: $e');
      throw ApiException(ApiError.timeoutError());
    } catch (e) {
      _logError('DELETE', url.toString(), 'Unknown error: $e');
      if (e is ApiException) rethrow;
      throw ApiException(ApiError.unknownError());
    }
  }

  // Register a new user
  Future<dynamic> register({
    required String email,
    required String username,
    required String password,
  }) async {
    final baseUrl = await _getBaseUrl();
    final url = Uri.parse(_joinUrl(baseUrl, '/api/auth/register'));
    final headers = _getAuthHeaders();
    final body = json.encode({
      'email': email,
      'username': username,
      'password': password,
    });

    try {
      // Log the request
      _logRequest('POST', url.toString(), headers, body);

      final response = await http
          .post(
            url,
            headers: headers,
            body: body,
          )
          .timeout(timeoutDuration);

      return _handleResponse('POST', url.toString(), response);
    } on SocketException catch (e) {
      _logError('POST', url.toString(), 'SocketException: $e');
      throw ApiException(ApiError.networkError());
    } on http.ClientException catch (e) {
      _logError('POST', url.toString(), 'ClientException: $e');
      throw ApiException(ApiError.timeoutError());
    } catch (e) {
      _logError('POST', url.toString(), 'Unknown error: $e');
      if (e is ApiException) rethrow;
      throw ApiException(ApiError.unknownError());
    }
  }
}
