import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'models/vehicle.dart';
import 'services/vehicle_service.dart';
import 'services/auth_service.dart';
import 'utils/app_theme.dart';
import 'package:flutter/foundation.dart';

class AddVehicleBottomSheet extends StatefulWidget {
  final VoidCallback onVehicleAdded;
  final Vehicle? vehicle;

  const AddVehicleBottomSheet({
    super.key,
    required this.onVehicleAdded,
    this.vehicle,
  });

  @override
  State<AddVehicleBottomSheet> createState() => _AddVehicleBottomSheetState();
}

class _AddVehicleBottomSheetState extends State<AddVehicleBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _modelController = TextEditingController();
  final _plateNumberController = TextEditingController();
  final _yearController = TextEditingController();
  final _fuelLevelController = TextEditingController();

  final VehicleService _vehicleService = VehicleService();
  final AuthService _authService = AuthService();

  late VehicleStatus _selectedStatus;
  late FuelType _selectedFuelType;
  late DateTime _insuranceExpiry;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Initialize with vehicle data if editing
    if (widget.vehicle != null) {
      _modelController.text = widget.vehicle!.model;
      _plateNumberController.text = widget.vehicle!.plateNumber;
      _yearController.text = widget.vehicle!.year.toString();
      _fuelLevelController.text = widget.vehicle!.fuelLevel.toString();
      _selectedStatus = widget.vehicle!.status;
      _selectedFuelType = widget.vehicle!.fuelType;
      _insuranceExpiry = widget.vehicle!.insuranceExpiry;
    } else {
      _selectedStatus = VehicleStatus.ACTIVE;
      _selectedFuelType = FuelType.GASOLINE;
      _insuranceExpiry = DateTime.now().add(const Duration(days: 365));
    }
  }

  @override
  void dispose() {
    _modelController.dispose();
    _plateNumberController.dispose();
    _yearController.dispose();
    _fuelLevelController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.vehicle != null;
    final screenHeight = MediaQuery.of(context).size.height;
    final bottomSheetHeight = screenHeight * 0.9;

    return Directionality(
      textDirection: ui.TextDirection.rtl,
      child: Container(
        height: bottomSheetHeight,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: AppTheme.backgroundLight,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 20,
              offset: const Offset(0, -10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 0, bottom: 16),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Title
            Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: Text(
                isEditing ? 'تعديل المركبة' : 'إضافة مركبة جديدة',
                style: AppTheme.getTitleStyle(),
              ),
            ),
            // Form
            Expanded(
              child: SingleChildScrollView(
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      TextFormField(
                        controller: _modelController,
                        decoration: InputDecoration(
                          labelText: 'موديل المركبة',
                          filled: true,
                          fillColor: AppTheme.backgroundWhite,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: const BorderSide(color: AppTheme.primaryColor, width: 1.5),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                          ),
                          focusedErrorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال موديل المركبة';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _plateNumberController,
                        decoration: InputDecoration(
                          labelText: 'رقم اللوحة',
                          filled: true,
                          fillColor: AppTheme.backgroundWhite,
                           border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: const BorderSide(color: AppTheme.primaryColor, width: 1.5),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                          ),
                          focusedErrorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال رقم اللوحة';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _yearController,
                        decoration: InputDecoration(
                          labelText: 'سنة الصنع',
                          filled: true,
                          fillColor: AppTheme.backgroundWhite,
                           border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: const BorderSide(color: AppTheme.primaryColor, width: 1.5),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                          ),
                          focusedErrorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                          ),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال سنة الصنع';
                          }
                          final year = int.tryParse(value);
                          if (year == null ||
                              year < 1900 ||
                              year > DateTime.now().year + 1) {
                            return 'يرجى إدخال سنة صحيحة';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: _fuelLevelController,
                        decoration: InputDecoration(
                          labelText: 'مستوى الوقود (%)',
                          filled: true,
                          fillColor: AppTheme.backgroundWhite,
                           border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: const BorderSide(color: AppTheme.primaryColor, width: 1.5),
                          ),
                          errorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                          ),
                          focusedErrorBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                            borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                          ),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال مستوى الوقود';
                          }
                          final level = double.tryParse(value);
                          if (level == null || level < 0 || level > 100) {
                            return 'يرجى إدخال قيمة بين 0 و 100';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      _buildDropdown<VehicleStatus>(
                        value: _selectedStatus,
                        label: 'حالة المركبة',
                        icon: Icons.info_outline,
                        items: VehicleStatus.values,
                        itemBuilder: (status) => _getStatusDisplayName(status),
                        onChanged: (value) =>
                            setState(() => _selectedStatus = value!),
                         decoration: InputDecoration(
                            labelText: 'حالة المركبة',
                             prefixIcon: Icon(Icons.info_outline, color: AppTheme.textMedium),
                             prefixIconConstraints: const BoxConstraints(minWidth: 48, minHeight: 48),
                             filled: true,
                              fillColor: AppTheme.backgroundWhite,
                               border: OutlineInputBorder(
                                 borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                                 borderSide: BorderSide.none,
                               ),
                               enabledBorder: OutlineInputBorder(
                                 borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                                 borderSide: BorderSide.none,
                               ),
                               focusedBorder: OutlineInputBorder(
                                 borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                                 borderSide: const BorderSide(color: AppTheme.primaryColor, width: 1.5),
                               ),
                               errorBorder: OutlineInputBorder(
                                 borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                                 borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                               ),
                               focusedErrorBorder: OutlineInputBorder(
                                 borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                                 borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                               ),
                          )
                      ),
                      const SizedBox(height: 16),
                      _buildDropdown<FuelType>(
                        value: _selectedFuelType,
                        label: 'نوع الوقود',
                        icon: Icons.local_gas_station_outlined,
                        items: FuelType.values,
                        itemBuilder: (type) => _getFuelTypeDisplayName(type),
                        onChanged: (value) =>
                            setState(() => _selectedFuelType = value!),
                         decoration: InputDecoration(
                            labelText: 'نوع الوقود',
                             prefixIcon: Icon(Icons.local_gas_station_outlined, color: AppTheme.textMedium),
                             prefixIconConstraints: const BoxConstraints(minWidth: 48, minHeight: 48),
                             filled: true,
                              fillColor: AppTheme.backgroundWhite,
                               border: OutlineInputBorder(
                                 borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                                 borderSide: BorderSide.none,
                               ),
                               enabledBorder: OutlineInputBorder(
                                 borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                                 borderSide: BorderSide.none,
                               ),
                               focusedBorder: OutlineInputBorder(
                                 borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                                 borderSide: const BorderSide(color: AppTheme.primaryColor, width: 1.5),
                               ),
                               errorBorder: OutlineInputBorder(
                                 borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                                 borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                               ),
                               focusedErrorBorder: OutlineInputBorder(
                                 borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                                 borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                               ),
                          )
                      ),
                      const SizedBox(height: 16),
                      _buildDatePicker(
                        label: 'تاريخ انتهاء التأمين',
                        selectedDate: _insuranceExpiry,
                        onDateSelected: (date) {
                          setState(() {
                            _insuranceExpiry = date;
                          });
                        },
                        decoration: InputDecoration(
                          labelText: 'تاريخ انتهاء التأمين',
                          prefixIcon: const Icon(Icons.calendar_today_outlined, color: AppTheme.textMedium),
                          prefixIconConstraints: const BoxConstraints(minWidth: 48, minHeight: 48),
                           filled: true,
                            fillColor: AppTheme.backgroundWhite,
                             border: OutlineInputBorder(
                               borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                               borderSide: BorderSide.none,
                             ),
                             enabledBorder: OutlineInputBorder(
                               borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                               borderSide: BorderSide.none,
                             ),
                             focusedBorder: OutlineInputBorder(
                               borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                               borderSide: const BorderSide(color: AppTheme.primaryColor, width: 1.5),
                             ),
                             errorBorder: OutlineInputBorder(
                               borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                               borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                             ),
                             focusedErrorBorder: OutlineInputBorder(
                               borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                               borderSide: const BorderSide(color: AppTheme.accentRed, width: 1.5),
                             ),
                        )
                      ),
                      const SizedBox(height: 24),
                      // Submit button
                      ElevatedButton(
                        onPressed: _isLoading ? null : _submitForm,
                        style: AppTheme.getPrimaryButtonStyle(),
                        child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor:
                                      AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : Text(isEditing ? 'حفظ التغييرات' : 'إضافة المركبة'),
                      ),
                      const SizedBox(height: 24),
                      if (isEditing)
                        TextButton(
                          onPressed: _isLoading ? null : _deleteVehicle,
                          style: TextButton.styleFrom(
                            foregroundColor: AppTheme.accentRed,
                          ),
                          child: _isLoading && !isEditing // Only show loading for deletion
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor:
                                      AlwaysStoppedAnimation<Color>(AppTheme.accentRed),
                                ),
                              )
                            : const Text('حذف المركبة'),
                        ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdown<T>({
    required T value,
    required String label,
    required IconData icon,
    required List<T> items,
    required String Function(T) itemBuilder,
    required void Function(T?) onChanged,
    required InputDecoration decoration,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: decoration,
      items: items.map((item) {
        return DropdownMenuItem(
          value: item,
          child: Text(itemBuilder(item)),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }

  Widget _buildDatePicker({
    required String label,
    required DateTime selectedDate,
    required ValueChanged<DateTime> onDateSelected,
    required InputDecoration decoration,
  }) {
    return InkWell(
      onTap: () async {
        final pickedDate = await showDatePicker(
          context: context,
          initialDate: selectedDate,
          firstDate: DateTime(2000),
          lastDate: DateTime(2101),
          builder: (context, child) {
            return Theme(
              data: ThemeData.light().copyWith(
                colorScheme: ColorScheme.light(
                  primary: AppTheme.primaryColor,
                  onPrimary: Colors.white,
                  surface: AppTheme.backgroundLight,
                  onSurface: AppTheme.textDark,
                ),
                dialogBackgroundColor: AppTheme.backgroundLight,
              ),
              child: Directionality(
                textDirection: ui.TextDirection.rtl,
                child: child!,
              ),
            );
          },
        );
        if (pickedDate != null && pickedDate != selectedDate) {
          onDateSelected(pickedDate);
        }
      },
      child: InputDecorator(
        decoration: decoration,
        baseStyle: TextStyle(
          fontSize: 16,
          color: AppTheme.textDark,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textDark,
              ),
            ),
            const Icon(Icons.arrow_drop_down, color: AppTheme.textMedium),
          ],
        ),
      ),
    );
  }

  String _getStatusDisplayName(VehicleStatus status) {
    switch (status) {
      case VehicleStatus.ACTIVE:
        return 'نشطة';
      case VehicleStatus.INACTIVE:
        return 'غير نشطة';
      case VehicleStatus.MAINTENANCE:
        return 'صيانة';
    }
  }

  String _getFuelTypeDisplayName(FuelType type) {
    switch (type) {
      case FuelType.GASOLINE:
        return 'بنزين';
      case FuelType.DIESEL:
        return 'ديزل';
      case FuelType.ELECTRIC:
        return 'كهرباء';
      case FuelType.HYBRID:
        return 'هجين';
    }
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      final accessToken = await _authService.getAccessToken();
      if (accessToken == null) {
        // Handle error: no access token
        setState(() {
          _isLoading = false;
        });
        if (kDebugMode) {
          print('No access token found');
        }
        return;
      }

      _vehicleService.setAccessToken(accessToken);

      try {
        if (widget.vehicle == null) {
          // Add new vehicle
          await _vehicleService.createVehicle(
             model: _modelController.text,
             plateNumber: _plateNumberController.text,
             year: int.parse(_yearController.text),
             fuelLevel: double.parse(_fuelLevelController.text),
             status: _selectedStatus,
             fuelType: _selectedFuelType,
             insuranceExpiry: _insuranceExpiry,
          );
        } else {
          // Update existing vehicle
          await _vehicleService.updateVehicle(
            id: widget.vehicle!.id,
             model: _modelController.text,
             plateNumber: _plateNumberController.text,
             year: int.parse(_yearController.text),
             fuelLevel: double.parse(_fuelLevelController.text),
             status: _selectedStatus,
             fuelType: _selectedFuelType,
             insuranceExpiry: _insuranceExpiry,
          );
        }

        widget.onVehicleAdded();
        Navigator.of(context).pop();
      } catch (e) {
        // Handle error
        if (kDebugMode) {
          print('Error saving vehicle: $e');
        }
        // Show error message to user
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save vehicle: ${e.toString()}'),
            backgroundColor: Colors.redAccent,
          ),
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteVehicle() async {
    if (widget.vehicle == null || widget.vehicle!.id == null) return;

     // Show confirmation dialog
    final bool confirm = await showDialog(
      context: context,
      builder: (BuildContext context) {
        return Directionality(
          textDirection: ui.TextDirection.rtl,
          child: AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text('هل أنت متأكد أنك تريد حذف هذه المركبة؟'),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.of(context).pop(false), // Pass false
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true), // Pass true
                child: const Text('حذف', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
        );
      },
    ) ?? false; // Default to false if dialog is dismissed

    if (!confirm) return;

    setState(() {
      _isLoading = true;
    });

    final accessToken = await _authService.getAccessToken();
    if (accessToken == null) {
      // Handle error: no access token
      setState(() {
        _isLoading = false;
      });
      if (kDebugMode) {
        print('No access token found');
      }
      return;
    }

    _vehicleService.setAccessToken(accessToken);

    try {
      await _vehicleService.deleteVehicle(widget.vehicle!.id!);
      widget.onVehicleAdded(); // Refresh vehicle list
      Navigator.of(context).pop(); // Close the bottom sheet
       // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حذف المركبة بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Handle error
      if (kDebugMode) {
        print('Error deleting vehicle: $e');
      }
      // Show error message to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to delete vehicle: ${e.toString()}'),
          backgroundColor: Colors.redAccent,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
