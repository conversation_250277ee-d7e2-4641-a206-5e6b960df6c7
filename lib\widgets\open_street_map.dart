import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;
import '../utils/responsive_size.dart';

// Define the classes outside of the main widget class
class MapMarker {
  final double latitude;
  final double longitude;
  final String? label;
  final IconData? icon;
  final Color? color;
  final Function? onTap;

  MapMarker({
    required this.latitude,
    required this.longitude,
    this.label,
    this.icon,
    this.color,
    this.onTap,
  });
}

class RoutePoint {
  final double latitude;
  final double longitude;

  RoutePoint({
    required this.latitude,
    required this.longitude,
  });
}

class MapRoute {
  final List<RoutePoint> points;
  final Color? color;
  final double? width;

  MapRoute({
    required this.points,
    this.color,
    this.width,
  });
}

class OpenStreetMapWidget extends StatefulWidget {
  final double initialLatitude;
  final double initialLongitude;
  final double zoom;
  final List<MapMarker>? markers;
  final MapRoute? route;
  final double height;
  final bool showAttribution;
  final bool interactive;

  const OpenStreetMapWidget({
    super.key,
    this.initialLatitude = 24.7136, // Default to Riyadh, Saudi Arabia
    this.initialLongitude = 46.6753,
    this.zoom = 13.0,
    this.markers,
    this.route,
    this.height = 200.0,
    this.showAttribution = true,
    this.interactive = true,
  });

  @override
  State<OpenStreetMapWidget> createState() => _OpenStreetMapWidgetState();
}

class _OpenStreetMapWidgetState extends State<OpenStreetMapWidget> {
  late final MapController _mapController;

  @override
  void initState() {
    super.initState();
    _mapController = MapController();
  }

  @override
  Widget build(BuildContext context) {
    ResponsiveSize().init(context);

    return SizedBox(
      height: widget.height,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(ResponsiveSize.width(2)),
        child: _buildMap(),
      ),
    );
  }

  // Define the darkModeTileBuilder function first
  Widget darkModeTileBuilder(
      BuildContext context, Widget tileWidget, TileImage tile) {
    return ColorFiltered(
      colorFilter: const ColorFilter.matrix([
        0.2126, 0.7152, 0.0722, 0, 0,
        0.2126, 0.7152, 0.0722, 0, 0,
        0.2126, 0.7152, 0.0722, 0, 0,
        0,      0,      0,      1, 0,
      ]),
      child: tileWidget,
    );
  }

  // Define the openUrl function to avoid naming conflict
  Future<void> openUrl(String urlString) async {
    final Uri uri = Uri.parse(urlString);
    if (!await url_launcher.launchUrl(uri)) {
      throw Exception('Could not launch $urlString');
    }
  }

  Widget _buildMap() {
    try {
      return FlutterMap(
        mapController: _mapController,
        options: MapOptions(
          initialCenter:
              LatLng(widget.initialLatitude, widget.initialLongitude),
          initialZoom: widget.zoom,
          interactionOptions: InteractionOptions(
            flags: widget.interactive
                ? InteractiveFlag.all
                : InteractiveFlag.none,
          ),
        ),
        children: [
          TileLayer(
            urlTemplate:
                'https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png',
            subdomains: const ['a', 'b', 'c'],
            userAgentPackageName: 'com.example.drivety',
            tileBuilder: widget.interactive ? null : darkModeTileBuilder,
            fallbackUrl: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
          ),
          if (widget.markers != null && widget.markers!.isNotEmpty)
            MarkerLayer(
              markers: widget.markers!.map((marker) {
                return Marker(
                  width: ResponsiveSize.width(10),
                  height: ResponsiveSize.width(15),
                  point: LatLng(marker.latitude, marker.longitude),
                  child: GestureDetector(
                    onTap: () {
                      if (marker.onTap != null) {
                        marker.onTap!();
                      }
                    },
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: marker.color ?? const Color(0xFFFF7E28),
                            shape: BoxShape.circle,
                          ),
                          padding: ResponsiveSize.paddingAll(1),
                          child: Icon(
                            marker.icon ?? Icons.location_on,
                            color: Colors.white,
                            size: ResponsiveSize.width(5),
                          ),
                        ),
                        if (marker.label != null)
                          Container(
                            margin: ResponsiveSize.paddingSymmetric(
                                vertical: 0.5),
                            padding: ResponsiveSize.paddingSymmetric(
                              horizontal: 1,
                              vertical: 0.5,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(
                                  ResponsiveSize.width(1)),
                            ),
                            child: Text(
                              marker.label!,
                              style: TextStyle(
                                fontSize: ResponsiveSize.fontSize(2.5),
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          if (widget.route != null)
            PolylineLayer(
              polylines: [
                Polyline(
                  points: widget.route!.points
                      .map((point) => LatLng(point.latitude, point.longitude))
                      .toList(),
                  color: widget.route!.color ?? const Color(0xFFFF7E28),
                  strokeWidth: widget.route!.width ?? 4.0,
                ),
              ],
            ),
          if (widget.showAttribution)
            RichAttributionWidget(
              attributions: [
                TextSourceAttribution(
                  'OpenStreetMap contributors',
                  onTap: () => openUrl('https://www.openstreetmap.org/copyright'),
                ),
              ],
            ),
        ],
      );
    } catch (e) {
      // Handle any errors that might occur when building the map
      return Center(
        child: Text('Error loading map: ${e.toString()}'),
      );
    }
  }

}

