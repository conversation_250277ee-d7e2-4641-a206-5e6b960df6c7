import 'package:flutter/material.dart';
import 'utils/responsive_size.dart';
import 'utils/app_theme.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      body: SafeArea(
        child: Directionality(
          textDirection: TextDirection.rtl,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                // Enhanced Header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: Container(
                        padding: const EdgeInsets.all(AppTheme.spacingSmall),
                        decoration: AppTheme.getCardDecoration(),
                        child: Icon(Icons.arrow_back_ios_new, 
                          color: AppTheme.primaryColor,
                          size: AppTheme.iconSizeSmall,
                        ),
                      ),
                    ),
                    Text(
                      'التنبيهات',
                      style: AppTheme.getTitleStyle(),
                    ),
                    const SizedBox(width: 40), // For balance
                  ],
                ),
                const SizedBox(height: 24),
                // Search Bar
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.search, color: Colors.grey[400]),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextField(
                          decoration: InputDecoration(
                            hintText: 'بحث في التنبيهات...',
                            border: InputBorder.none,
                            hintStyle: TextStyle(
                              color: Colors.grey[400],
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                Expanded(
                  child: ListView(
                    children: [
                      _buildNotificationItem(
                        icon: Icons.check_circle_outline,
                        color: AppTheme.accentYellow,
                        message: 'تحديث بمحتويات السيارة متوفر',
                        time: 'قبل 3 ساعات',
                      ),
                      const SizedBox(height: 16),
                      _buildNotificationItem(
                        icon: Icons.warning_amber_outlined,
                        color: AppTheme.accentYellow,
                        message: 'تذكير فحص ضغط الإطارات',
                        time: 'قبل 3 ساعات',
                      ),
                      const SizedBox(height: 16),
                      _buildNotificationItem(
                        icon: Icons.settings,
                        color: AppTheme.accentYellow,
                        message: 'موعد الصيانة القادم: 3 أيام',
                        time: 'قبل 5 ساعات',
                      ),
                      const SizedBox(height: 16),
                      _buildNotificationItem(
                        icon: Icons.local_gas_station,
                        color: AppTheme.accentGreen,
                        message: 'مستوى الوقود منخفض',
                        time: 'قبل 6 ساعات',
                      ),
                      const SizedBox(height: 16),
                      _buildNotificationItem(
                        icon: Icons.speed,
                        color: AppTheme.accentRed,
                        message: 'تجاوز الحد الأقصى للسرعة',
                        time: 'قبل 8 ساعات',
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationItem({
    required IconData icon,
    required Color color,
    required String message,
    required String time,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  message,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1A1A1A),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  time,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: Colors.grey[400],
            size: 16,
          ),
        ],
      ),
    );
  }
} 