import 'package:flutter/foundation.dart';
import '../models/trip.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import 'api_error.dart';
import 'location_service.dart';
import 'reports_storage_service.dart';

class TripService {
  final ApiService _apiService = ApiService();
  final ReportsStorageService _storageService = ReportsStorageService();

  // Set access token for authenticated requests
  void setAccessToken(String token) {
    _apiService.setAccessToken(token);
  }

  // Start a new trip
  Future<Trip> startTrip({
    required String driverId,
    required String vehicleId,
    required String startLocation,
    required double startLatitude,
    required double startLongitude,
  }) async {
    if (kDebugMode) {
      print('🛣️ TRIP: Starting new trip for driver: $driverId');
    }

    try {
      final tripData = {
        'driverId': driverId,
        'vehicleId': vehicleId,
        'startLocation': startLocation,
        'startLatitude': startLatitude,
        'startLongitude': startLongitude,
      };

      final response = await _apiService.post('/api/trips/start', tripData);

      if (kDebugMode) {
        print('🛣️ TRIP: Start trip response: $response');
      }

      final apiResponse = ApiResponse<Trip>.fromJson(
        response,
        (data) => Trip.fromJson(data as Map<String, dynamic>),
      );

      if (apiResponse.success && apiResponse.data != null) {
        return apiResponse.data!;
      } else {
        throw ApiException(ApiError(
          message: 'Failed to start trip',
          details: apiResponse.error ?? apiResponse.message,
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('🛣️ TRIP: Error starting trip: $e');
      }
      rethrow;
    }
  }

  // End a trip
  Future<Trip> endTrip({
    required String tripId,
    required String endLocation,
    required double endLatitude,
    required double endLongitude,
    double? startLatitude,
    double? startLongitude,
  }) async {
    if (kDebugMode) {
      print('🛣️ TRIP: Ending trip: $tripId');
    }

    try {
      // Calculate distance if start coordinates are provided
      double calculatedDistance = 0.0;
      if (startLatitude != null && startLongitude != null) {
        calculatedDistance = LocationService.calculateDistance(
          startLatitude,
          startLongitude,
          endLatitude,
          endLongitude,
        );
      }

      // Set fuel consumption to 50 as requested
      const fuelConsumed = 50.0;

      if (kDebugMode) {
        print(
            '🛣️ TRIP: Calculated distance: ${calculatedDistance.toStringAsFixed(2)} km');
        print('🛣️ TRIP: Set fuel consumption: $fuelConsumed liters');
      }

      // Include distance and fuel consumption in the request body
      final tripData = {
        'endLocation': endLocation,
        'endLatitude': endLatitude,
        'endLongitude': endLongitude,
        'distance': calculatedDistance,
        'fuelConsumed': fuelConsumed,
      };

      if (kDebugMode) {
        print('🛣️ TRIP: Sending end trip data: $tripData');
      }

      final response =
          await _apiService.put('/api/trips/$tripId/end', tripData);

      if (kDebugMode) {
        print('🛣️ TRIP: End trip response: $response');
      }

      // Handle the standard API response format
      if (response is Map<String, dynamic>) {
        // Check if it's a standard API response with success and data
        if (response.containsKey('success') && response.containsKey('data')) {
          final apiResponse = ApiResponse<Trip>.fromJson(
            response,
            (data) {
              final tripData =
                  Map<String, dynamic>.from(data as Map<String, dynamic>);

              // Ensure distance and fuelConsumed are not null
              if (tripData['distance'] == null) {
                tripData['distance'] = calculatedDistance;
                if (kDebugMode) {
                  print(
                      '🛣️ TRIP: Set distance to calculated value: $calculatedDistance km');
                }
              }
              if (tripData['fuelConsumed'] == null) {
                tripData['fuelConsumed'] = fuelConsumed;
                if (kDebugMode) {
                  print(
                      '🛣️ TRIP: Set fuel consumption to: $fuelConsumed liters');
                }
              }

              // Ensure all required fields are present
              if (!tripData.containsKey('id') || tripData['id'] == null) {
                tripData['id'] = tripId;
              }
              if (!tripData.containsKey('vehicleId') ||
                  tripData['vehicleId'] == null) {
                throw Exception('Missing required field: vehicleId');
              }
              if (!tripData.containsKey('driverId') ||
                  tripData['driverId'] == null) {
                throw Exception('Missing required field: driverId');
              }
              if (!tripData.containsKey('startTime') ||
                  tripData['startTime'] == null) {
                throw Exception('Missing required field: startTime');
              }
              if (!tripData.containsKey('updatedAt') ||
                  tripData['updatedAt'] == null) {
                tripData['updatedAt'] = DateTime.now().toIso8601String();
              }

              if (kDebugMode) {
                print('🛣️ TRIP: Final trip data for parsing: $tripData');
              }

              return Trip.fromJson(tripData);
            },
          );

          if (apiResponse.success && apiResponse.data != null) {
            return apiResponse.data!;
          } else {
            throw ApiException(ApiError(
              message: 'Failed to end trip',
              details: apiResponse.error ?? apiResponse.message,
            ));
          }
        }
        // Handle direct response format (new format)
        else if (response.containsKey('endLocation') &&
            response.containsKey('endLatitude') &&
            response.containsKey('endLongitude')) {
          // Ensure we have all required fields for Trip creation
          final tripResponseData = Map<String, dynamic>.from(response);

          // Set required fields
          tripResponseData['id'] = tripId;
          tripResponseData['distance'] =
              response['distance'] ?? calculatedDistance;
          tripResponseData['fuelConsumed'] =
              response['fuelConsumed'] ?? fuelConsumed;
          tripResponseData['status'] = 'COMPLETED';
          tripResponseData['endTime'] = DateTime.now().toIso8601String();
          tripResponseData['updatedAt'] = DateTime.now().toIso8601String();

          // Add start coordinates if available
          if (startLatitude != null && startLongitude != null) {
            tripResponseData['startLatitude'] = startLatitude;
            tripResponseData['startLongitude'] = startLongitude;
          }

          // Validate required fields
          if (!tripResponseData.containsKey('vehicleId') ||
              tripResponseData['vehicleId'] == null) {
            throw Exception('Missing required field: vehicleId in response');
          }
          if (!tripResponseData.containsKey('driverId') ||
              tripResponseData['driverId'] == null) {
            throw Exception('Missing required field: driverId in response');
          }
          if (!tripResponseData.containsKey('startTime') ||
              tripResponseData['startTime'] == null) {
            throw Exception('Missing required field: startTime in response');
          }

          if (kDebugMode) {
            print(
                '🛣️ TRIP: Final direct response data for parsing: $tripResponseData');
          }

          final trip = Trip.fromJson(tripResponseData);

          // Update the cache with the calculated values
          await _storageService.updateTripData(
            tripId,
            distance: calculatedDistance,
            fuelConsumed: fuelConsumed,
            endLocation: endLocation,
            endLatitude: endLatitude,
            endLongitude: endLongitude,
            endTime: DateTime.now(),
            status: 'COMPLETED',
          );

          return trip;
        } else {
          throw Exception(
              'Invalid response format: missing required fields (endLocation, endLatitude, endLongitude)');
        }
      } else {
        throw Exception(
            'Invalid response format: expected Map<String, dynamic>');
      }
    } catch (e) {
      if (kDebugMode) {
        print('🛣️ TRIP: Error ending trip: $e');
      }
      rethrow;
    }
  }

  // Get trips for a specific driver
  Future<List<Trip>> getDriverTrips(String driverId) async {
    if (kDebugMode) {
      print('🛣️ TRIP: Getting trips for driver: $driverId');
    }

    try {
      final response = await _apiService.get('/api/trips/driver/$driverId');

      if (kDebugMode) {
        print('🛣️ TRIP: Driver trips response: $response');
      }

      final apiResponse = ApiResponse<List<Trip>>.fromJson(
        response,
        (data) => (data as List<dynamic>)
            .map((trip) => Trip.fromJson(trip as Map<String, dynamic>))
            .toList(),
      );

      if (apiResponse.success && apiResponse.data != null) {
        final trips = apiResponse.data!;

        if (kDebugMode) {
          print('🛣️ TRIP: Fetched ${trips.length} trips for driver');
          for (final trip in trips) {
            print(
                '🛣️ TRIP API: ${trip.id} - Distance: ${trip.distance}km, Fuel: ${trip.fuelConsumed}L, Status: ${trip.status.name}');
          }
        }

        return trips;
      } else {
        throw ApiException(ApiError(
          message: 'Failed to get driver trips',
          details: apiResponse.error ?? apiResponse.message,
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('🛣️ TRIP: Error getting driver trips: $e');
      }
      rethrow;
    }
  }

  // Get real street name from coordinates
  Future<String> getStreetNameFromCoordinates(
    double latitude,
    double longitude,
  ) async {
    try {
      return await LocationService.getAddressFromCoordinates(
          latitude, longitude);
    } catch (e) {
      if (kDebugMode) {
        print('🛣️ TRIP: Error getting street name: $e');
      }
      return 'Lat: ${latitude.toStringAsFixed(6)}, Lng: ${longitude.toStringAsFixed(6)}';
    }
  }

  // Enhanced start trip with real location names
  Future<Trip> startTripWithRealLocation({
    required String driverId,
    required String vehicleId,
    required double startLatitude,
    required double startLongitude,
  }) async {
    try {
      // Get real street name for start location
      final startLocation = await getStreetNameFromCoordinates(
        startLatitude,
        startLongitude,
      );

      return await startTrip(
        driverId: driverId,
        vehicleId: vehicleId,
        startLocation: startLocation,
        startLatitude: startLatitude,
        startLongitude: startLongitude,
      );
    } catch (e) {
      if (kDebugMode) {
        print('🛣️ TRIP: Error starting trip with real location: $e');
      }
      rethrow;
    }
  }

  // Enhanced end trip with real location names and distance calculation
  Future<Trip> endTripWithRealLocation({
    required String tripId,
    required double endLatitude,
    required double endLongitude,
    double? startLatitude,
    double? startLongitude,
  }) async {
    try {
      // Get real street name for end location
      final endLocation = await getStreetNameFromCoordinates(
        endLatitude,
        endLongitude,
      );

      return await endTrip(
        tripId: tripId,
        endLocation: endLocation,
        endLatitude: endLatitude,
        endLongitude: endLongitude,
        startLatitude: startLatitude,
        startLongitude: startLongitude,
      );
    } catch (e) {
      if (kDebugMode) {
        print('🛣️ TRIP: Error ending trip with real location: $e');
      }
      rethrow;
    }
  }
}
