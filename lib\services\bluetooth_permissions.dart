import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_bluetooth_serial/flutter_bluetooth_serial.dart';
import 'dart:io';

enum BluetoothPermissionStatus {
  granted,
  denied,
  permanentlyDenied,
  bluetoothDisabled,
  locationDisabled,
}

class BluetoothPermissionResult {
  final BluetoothPermissionStatus status;
  final String message;
  final List<Permission> deniedPermissions;

  BluetoothPermissionResult({
    required this.status,
    required this.message,
    this.deniedPermissions = const [],
  });
}

class BluetoothPermissions {
  /// Check if all required Bluetooth permissions are granted
  static Future<BluetoothPermissionResult> checkPermissions() async {
    try {
      // Check if Bluetooth is available
      bool isAvailable = await FlutterBluetoothSerial.instance.isAvailable ?? false;
      if (!isAvailable) {
        return BluetoothPermissionResult(
          status: BluetoothPermissionStatus.denied,
          message: 'البلوتوث غير متوفر على هذا الجهاز',
        );
      }

      // Check if Bluetooth is enabled
      bool isEnabled = await FlutterBluetoothSerial.instance.isEnabled ?? false;
      if (!isEnabled) {
        return BluetoothPermissionResult(
          status: BluetoothPermissionStatus.bluetoothDisabled,
          message: 'البلوتوث غير مفعل. يرجى تفعيل البلوتوث للمتابعة',
        );
      }

      // Check location permissions (required for Bluetooth device discovery)
      List<Permission> requiredPermissions = [
        Permission.locationWhenInUse,
      ];

      // Add Bluetooth permissions based on Android version
      if (Platform.isAndroid) {
        // For Android 12+ (API 31+)
        requiredPermissions.addAll([
          Permission.bluetoothScan,
          Permission.bluetoothConnect,
        ]);
      }

      List<Permission> deniedPermissions = [];

      for (Permission permission in requiredPermissions) {
        PermissionStatus status = await permission.status;
        if (status != PermissionStatus.granted) {
          deniedPermissions.add(permission);
        }
      }

      if (deniedPermissions.isNotEmpty) {
        // Check if any permissions are permanently denied
        bool hasPermanentlyDenied = false;
        for (Permission permission in deniedPermissions) {
          if (await permission.isPermanentlyDenied) {
            hasPermanentlyDenied = true;
            break;
          }
        }

        return BluetoothPermissionResult(
          status: hasPermanentlyDenied
            ? BluetoothPermissionStatus.permanentlyDenied
            : BluetoothPermissionStatus.denied,
          message: hasPermanentlyDenied
            ? 'تم رفض الأذونات نهائياً. يرجى تفعيلها من إعدادات التطبيق'
            : 'يتطلب التطبيق أذونات البلوتوث والموقع للعمل بشكل صحيح',
          deniedPermissions: deniedPermissions,
        );
      }

      return BluetoothPermissionResult(
        status: BluetoothPermissionStatus.granted,
        message: 'جميع الأذونات مفعلة',
      );
    } catch (e) {
      return BluetoothPermissionResult(
        status: BluetoothPermissionStatus.denied,
        message: 'خطأ في فحص الأذونات: ${e.toString()}',
      );
    }
  }

  /// Request all required Bluetooth permissions
  static Future<BluetoothPermissionResult> requestPermissions() async {
    try {
      // First check current status
      BluetoothPermissionResult currentStatus = await checkPermissions();

      if (currentStatus.status == BluetoothPermissionStatus.granted) {
        return currentStatus;
      }

      if (currentStatus.status == BluetoothPermissionStatus.bluetoothDisabled) {
        return currentStatus;
      }

      if (currentStatus.status == BluetoothPermissionStatus.permanentlyDenied) {
        return currentStatus;
      }

      // Request permissions
      List<Permission> permissionsToRequest = currentStatus.deniedPermissions;

      if (permissionsToRequest.isNotEmpty) {
        Map<Permission, PermissionStatus> results = await permissionsToRequest.request();

        List<Permission> stillDenied = [];
        bool hasPermanentlyDenied = false;

        for (MapEntry<Permission, PermissionStatus> entry in results.entries) {
          if (entry.value != PermissionStatus.granted) {
            stillDenied.add(entry.key);
            if (entry.value == PermissionStatus.permanentlyDenied) {
              hasPermanentlyDenied = true;
            }
          }
        }

        if (stillDenied.isNotEmpty) {
          return BluetoothPermissionResult(
            status: hasPermanentlyDenied
              ? BluetoothPermissionStatus.permanentlyDenied
              : BluetoothPermissionStatus.denied,
            message: hasPermanentlyDenied
              ? 'تم رفض بعض الأذونات نهائياً. يرجى تفعيلها من إعدادات التطبيق'
              : 'تم رفض بعض الأذونات المطلوبة',
            deniedPermissions: stillDenied,
          );
        }
      }

      // Check again after requesting permissions
      return await checkPermissions();
    } catch (e) {
      return BluetoothPermissionResult(
        status: BluetoothPermissionStatus.denied,
        message: 'خطأ في طلب الأذونات: ${e.toString()}',
      );
    }
  }

  /// Enable Bluetooth if it's disabled
  static Future<bool> enableBluetooth() async {
    try {
      bool isEnabled = await FlutterBluetoothSerial.instance.isEnabled ?? false;
      if (isEnabled) return true;

      // Request to enable Bluetooth
      bool? result = await FlutterBluetoothSerial.instance.requestEnable();
      return result ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Open app settings for manual permission management
  static Future<bool> openSettings() async {
    return await openAppSettings();
  }

  /// Open Bluetooth settings
  static Future<void> openBluetoothSettings() async {
    try {
      await FlutterBluetoothSerial.instance.openSettings();
    } catch (e) {
      // Fallback: this will be handled by the UI
    }
  }

  /// Get user-friendly permission names for display
  static String getPermissionDisplayName(Permission permission) {
    switch (permission) {
      case Permission.bluetoothScan:
        return 'البحث عن أجهزة البلوتوث';
      case Permission.bluetoothConnect:
        return 'الاتصال بأجهزة البلوتوث';
      case Permission.locationWhenInUse:
        return 'الوصول للموقع (مطلوب للبلوتوث)';
      default:
        return permission.toString();
    }
  }

  /// Get all available Bluetooth devices (both bonded and discovered)
  static Future<List<BluetoothDevice>> getAllBluetoothDevices({
    bool includeBonded = true,
    bool includeDiscovered = true,
    Duration scanTimeout = const Duration(seconds: 10),
  }) async {
    try {
      List<BluetoothDevice> allDevices = [];

      // Get bonded devices first
      if (includeBonded) {
        List<BluetoothDevice> bondedDevices = await FlutterBluetoothSerial.instance.getBondedDevices();
        allDevices.addAll(bondedDevices);
      }

      // Discover new devices if requested
      if (includeDiscovered) {
        List<BluetoothDevice> discoveredDevices = await discoverBluetoothDevices(scanTimeout);

        // Add discovered devices that aren't already in bonded list
        for (BluetoothDevice device in discoveredDevices) {
          bool alreadyExists = allDevices.any((existing) => existing.address == device.address);
          if (!alreadyExists) {
            allDevices.add(device);
          }
        }
      }

      return allDevices;
    } catch (e) {
      throw Exception('خطأ في البحث عن الأجهزة: ${e.toString()}');
    }
  }

  /// Discover new Bluetooth devices
  static Future<List<BluetoothDevice>> discoverBluetoothDevices(Duration timeout) async {
    try {
      List<BluetoothDevice> discoveredDevices = [];

      // Start discovery and listen for results
      Stream<BluetoothDiscoveryResult> discoveryStream = FlutterBluetoothSerial.instance.startDiscovery();

      // Create a subscription to handle the stream properly
      var subscription = discoveryStream.listen(
        (BluetoothDiscoveryResult result) {
          discoveredDevices.add(result.device);
          print('🔍 DISCOVERY: Found device: ${result.device.name ?? "Unknown"} (${result.device.address})');
        },
        onError: (error) {
          print('❌ DISCOVERY ERROR: $error');
        },
        onDone: () {
          print('✅ DISCOVERY: Completed');
        },
      );

      // Wait for the specified timeout
      await Future.delayed(timeout);

      // Cancel the subscription and stop discovery
      await subscription.cancel();

      // Make sure discovery is stopped
      try {
        await FlutterBluetoothSerial.instance.cancelDiscovery();
      } catch (e) {
        // Ignore errors when canceling discovery
      }

      print('🔍 DISCOVERY: Found ${discoveredDevices.length} new devices');
      return discoveredDevices;
    } catch (e) {
      print('❌ DISCOVERY: Error during discovery: $e');
      // Make sure discovery is stopped even on error
      try {
        await FlutterBluetoothSerial.instance.cancelDiscovery();
      } catch (e) {
        // Ignore errors when canceling discovery
      }
      return []; // Return empty list instead of throwing
    }
  }

  /// Check if a device is likely an OBD-II device based on name patterns
  static bool isLikelyOBDDevice(BluetoothDevice device) {
    if (device.name == null || device.name!.isEmpty) return false;

    String deviceName = device.name!.toLowerCase();

    // Common OBD-II device name patterns
    List<String> obdPatterns = [
      'obd', 'elm', '327', 'obdii', 'obd-ii', 'obd2', 'obd-2',
      'scan', 'diagnostic', 'auto', 'car', 'vehicle', 'torque',
      'bluetooth', 'bt', 'adapter', 'dongle', 'scanner',
      'v1.5', 'v2.1', 'mini', 'super', 'advanced'
    ];

    return obdPatterns.any((pattern) => deviceName.contains(pattern));
  }

  /// Get filtered OBD devices with option to show all devices
  static List<BluetoothDevice> filterOBDDevices(List<BluetoothDevice> devices, {bool strictFilter = false}) {
    if (strictFilter) {
      // Strict filtering - only devices that clearly look like OBD devices
      return devices.where((device) => isLikelyOBDDevice(device)).toList();
    } else {
      // Show all devices - let user decide
      return devices;
    }
  }

  /// Get device type description for display
  static String getDeviceTypeDescription(BluetoothDevice device) {
    if (device.name == null || device.name!.isEmpty) {
      return 'جهاز غير معروف';
    }

    if (isLikelyOBDDevice(device)) {
      return 'جهاز OBD محتمل';
    }

    String deviceName = device.name!.toLowerCase();

    if (deviceName.contains('headphone') || deviceName.contains('earphone') || deviceName.contains('buds')) {
      return 'سماعات';
    } else if (deviceName.contains('speaker')) {
      return 'مكبر صوت';
    } else if (deviceName.contains('keyboard')) {
      return 'لوحة مفاتيح';
    } else if (deviceName.contains('mouse')) {
      return 'فأرة';
    } else if (deviceName.contains('phone')) {
      return 'هاتف';
    } else {
      return 'جهاز بلوتوث';
    }
  }
}