import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import 'models/vehicle.dart';
import 'models/driver.dart';
import 'services/vehicle_service.dart';
import 'services/driver_service.dart';
import 'services/auth_service.dart';
import 'add_vehicle_bottom_sheet.dart';
import 'utils/app_theme.dart';
import 'utils/responsive_size.dart';
import 'widgets/obd_connection_dialog.dart';
import 'services/obd_service.dart';
import 'package:flutter/foundation.dart';

class VehicleScreen extends StatefulWidget {
  const VehicleScreen({super.key});

  @override
  State<VehicleScreen> createState() => _VehicleScreenState();
}

class _VehicleScreenState extends State<VehicleScreen> {
  final VehicleService _vehicleService = VehicleService();
  final DriverService _driverService = DriverService();
  final AuthService _authService = AuthService();

  Driver? _currentDriver;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final accessToken = await _authService.getAccessToken();
      if (accessToken == null) {
        throw Exception('No access token found');
      }

      if (kDebugMode) {
        print('🚗 VEHICLE SCREEN: Loading data with access token');
      }

      _driverService.setAccessToken(accessToken);
      _vehicleService.setAccessToken(accessToken);

      final driver = await _driverService.getCurrentUserDriver();
      
      if (kDebugMode) {
        print('🚗 VEHICLE SCREEN: Driver data loaded:');
        print('Driver ID: ${driver.id}');
        print('Number of vehicles: ${driver.vehicles.length}');
        for (final vehicle in driver.vehicles) {
          print('Vehicle: ${vehicle.model} (${vehicle.plateNumber})');
        }
      }

      if (!mounted) return;

      setState(() {
        _currentDriver = driver;
        _isLoading = false;
      });
    } catch (e) {
      if (kDebugMode) {
        print('🚗 VEHICLE SCREEN: Error loading data: $e');
      }
      if (!mounted) return;

      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteVehicle(Vehicle vehicle) async {
    try {
      await _vehicleService.deleteVehicle(vehicle.id);
      await _loadData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف المركبة بنجاح'),
            backgroundColor: AppTheme.accentGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف المركبة: ${e.toString()}'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    }
  }

  Future<void> _setActiveVehicle(Vehicle vehicle) async {
    try {
      await _vehicleService.setActiveVehicle(vehicle.id);
      await _loadData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تعيين المركبة النشطة بنجاح'),
            backgroundColor: AppTheme.accentGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تعيين المركبة النشطة: ${e.toString()}'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    }
  }

  void _showEditVehicleBottomSheet(Vehicle vehicle) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddVehicleBottomSheet(
        vehicle: vehicle,
        onVehicleAdded: () {
          _loadData();
        },
      ),
    );
  }

  void _showDeleteConfirmationDialog(Vehicle vehicle) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المركبة'),
        content: const Text('هل أنت متأكد من حذف هذه المركبة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteVehicle(vehicle);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.accentRed,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  Future<void> _showOBDConnectionDialog() async {
    // Initialize ResponsiveSize if not already initialized
    if (!ResponsiveSize.isInitialized) {
      ResponsiveSize().init(context);
    }

    try {
      final result = await showOBDConnectionDialog(context);
      if (result == true && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم الاتصال بجهاز OBD بنجاح'),
            backgroundColor: AppTheme.accentGreen,
          ),
        );
        // Refresh the UI to update the button state
        setState(() {});
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل الاتصال بجهاز OBD: ${e.toString()}'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      appBar: AppBar(
        backgroundColor: AppTheme.backgroundWhite,
        elevation: 0,
        title: Text(
          'مركباتي',
          style: AppTheme.getTitleStyle(),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: ElevatedButton.icon(
              onPressed: _showAddVehicleBottomSheet,
              icon: const Icon(Icons.add, size: 18),
              label: const Text('إضافة مركبة'),
              style: AppTheme.getPrimaryButtonStyle(),
            ),
          ),
        ],
      ),
      body: Directionality(
        textDirection: ui.TextDirection.rtl,
        child: RefreshIndicator(
          onRefresh: _loadData,
          color: AppTheme.primaryColor,
          child: _buildContent(),
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryColor,
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppTheme.accentRedLight,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.error_outline,
                  size: 48,
                  color: AppTheme.accentRed,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                'حدث خطأ في تحميل المركبات',
                style: AppTheme.getSubtitleStyle(),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage!,
                style: AppTheme.getBodyStyle(),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadData,
                style: AppTheme.getPrimaryButtonStyle(),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    if (_currentDriver == null || _currentDriver!.vehicles.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: AppTheme.primaryLightColor,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.directions_car_outlined,
                  size: 64,
                  color: AppTheme.primaryColor,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'لا توجد مركبات',
                style: AppTheme.getTitleStyle(),
              ),
              const SizedBox(height: 12),
              Text(
                'ابدأ بإضافة مركبتك الأولى لتتمكن من إدارة رحلاتك',
                style: AppTheme.getBodyStyle(),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: _showAddVehicleBottomSheet,
                icon: const Icon(Icons.add_circle_outline),
                label: const Text('إضافة مركبة جديدة'),
                style: AppTheme.getPrimaryButtonStyle(),
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _currentDriver!.vehicles.length,
      itemBuilder: (context, index) {
        return _buildVehicleCard(_currentDriver!.vehicles[index]);
      },
    );
  }

  Widget _buildVehicleCard(Vehicle vehicle) {
    final bool isActive = vehicle.status == VehicleStatus.ACTIVE;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: AppTheme.getCardDecoration(),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
                colors: [
                  isActive ? AppTheme.accentGreen : AppTheme.accentYellow,
                  isActive ? AppTheme.accentGreenLight : AppTheme.accentYellowLight,
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppTheme.borderRadiusMedium),
                topRight: Radius.circular(AppTheme.borderRadiusMedium),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                  ),
                  child: const Icon(
                    Icons.directions_car,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        vehicle.model,
                        style: TextStyle(
                          color: isActive ? AppTheme.accentGreenDark : AppTheme.accentYellowDark,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'سنة الصنع: ${vehicle.year}',
                        style: TextStyle(
                          color: isActive ? AppTheme.accentGreenDark.withOpacity(0.9) : AppTheme.accentYellowDark.withOpacity(0.9),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isActive)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                    ),
                    child: Text(
                      'نشط',
                      style: TextStyle(
                        color: AppTheme.accentGreenDark,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoCard(
                        icon: Icons.confirmation_number,
                        title: 'رقم اللوحة',
                        value: vehicle.plateNumber,
                        color: AppTheme.accentBlue,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildInfoCard(
                        icon: Icons.local_gas_station,
                        title: 'نوع الوقود',
                        value: vehicle.fuelTypeDisplayName,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                _buildFuelIndicator(vehicle),
                const SizedBox(height: 12),
                _buildInfoCard(
                  icon: Icons.security,
                  title: 'انتهاء التأمين',
                  value: '${vehicle.insuranceExpiry.day}/${vehicle.insuranceExpiry.month}/${vehicle.insuranceExpiry.year}',
                  color: AppTheme.accentBlue,
                  fullWidth: true,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _showEditVehicleBottomSheet(vehicle),
                        icon: const Icon(Icons.edit),
                        label: const Text('تعديل'),
                        style: AppTheme.getSecondaryButtonStyle(),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _showDeleteConfirmationDialog(vehicle),
                        icon: const Icon(Icons.delete),
                        label: const Text('حذف'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppTheme.accentRed,
                          side: const BorderSide(color: AppTheme.accentRed),
                        ),
                      ),
                    ),
                  ],
                ),
                if (!isActive) ...[
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _setActiveVehicle(vehicle),
                      icon: const Icon(Icons.check_circle),
                      label: const Text('تعيين كمركبة نشطة'),
                      style: AppTheme.getPrimaryButtonStyle(),
                    ),
                  ),
                ],
                if (isActive) ...[
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _showOBDConnectionDialog,
                      icon: const Icon(Icons.bluetooth),
                      label: Text(OBDService.isConnected() ? 'متصل بـ OBD' : 'اتصال OBD'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: OBDService.isConnected()
                          ? AppTheme.accentGreen
                          : AppTheme.accentBlue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
    bool fullWidth = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: fullWidth
          ? Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTheme.getBodyStyle(),
                      ),
                      Text(
                        value,
                        style: TextStyle(
                          fontSize: AppTheme.fontSizeMedium,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            )
          : Column(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: AppTheme.getBodyStyle(),
                  textAlign: TextAlign.center,
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: AppTheme.fontSizeMedium,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
    );
  }

  Widget _buildFuelIndicator(Vehicle vehicle) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.backgroundLight,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'مستوى الوقود',
                style: AppTheme.getBodyStyle(),
              ),
              Text(
                '${vehicle.fuelLevel.toStringAsFixed(1)}%',
                style: TextStyle(
                  fontSize: AppTheme.fontSizeMedium,
                  fontWeight: FontWeight.bold,
                  color: _getFuelLevelColor(vehicle.fuelLevel),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: vehicle.fuelLevel / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              _getFuelLevelColor(vehicle.fuelLevel),
            ),
            minHeight: 8,
          ),
        ],
      ),
    );
  }

  Color _getFuelLevelColor(double fuelLevel) {
    if (fuelLevel > 50) {
      return AppTheme.accentGreen;
    } else if (fuelLevel > 25) {
      return AppTheme.accentYellow;
    } else {
      return AppTheme.accentRed;
    }
  }

  void _showAddVehicleBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddVehicleBottomSheet(
        onVehicleAdded: () {
          _loadData();
        },
      ),
    );
  }
}

// Custom painter for geometric pattern
class GeometricPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFE2E8F0).withOpacity(0.3)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    const spacing = 40.0;

    // Draw diagonal lines
    for (double i = -size.height; i < size.width + size.height; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i + size.height, size.height),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class AddVehicleDialog extends StatefulWidget {
  final VoidCallback onVehicleAdded;

  const AddVehicleDialog({
    super.key,
    required this.onVehicleAdded,
  });

  @override
  State<AddVehicleDialog> createState() => _AddVehicleDialogState();
}

class _AddVehicleDialogState extends State<AddVehicleDialog> {
  final _formKey = GlobalKey<FormState>();
  final _modelController = TextEditingController();
  final _yearController = TextEditingController();
  final _plateController = TextEditingController();
  final _fuelLevelController = TextEditingController();

  final VehicleService _vehicleService = VehicleService();
  final AuthService _authService = AuthService();

  final VehicleStatus _selectedStatus = VehicleStatus.ACTIVE;
  FuelType _selectedFuelType = FuelType.GASOLINE;
  final DateTime _insuranceExpiry =
      DateTime.now().add(const Duration(days: 365));
  bool _isLoading = false;

  @override
  void dispose() {
    _modelController.dispose();
    _yearController.dispose();
    _plateController.dispose();
    _fuelLevelController.dispose();
    super.dispose();
  }

  Future<void> _addVehicle() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Get access token
      final accessToken = await _authService.getAccessToken();
      if (accessToken == null) {
        throw Exception('No access token found');
      }

      _vehicleService.setAccessToken(accessToken);

      await _vehicleService.createVehicle(
        model: _modelController.text,
        year: int.parse(_yearController.text),
        plateNumber: _plateController.text,
        status: _selectedStatus,
        fuelType: _selectedFuelType,
        fuelLevel: double.parse(_fuelLevelController.text),
        insuranceExpiry: _insuranceExpiry,
      );

      if (mounted) {
        Navigator.of(context).pop();
        widget.onVehicleAdded();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة المركبة بنجاح'),
            backgroundColor: Color(0xFF4CAF50),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة المركبة: ${e.toString()}'),
            backgroundColor: const Color(0xFFF44336),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: AlertDialog(
        title: const Text('إضافة مركبة جديدة'),
        content: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _modelController,
                  decoration: const InputDecoration(
                    labelText: 'موديل المركبة',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال موديل المركبة';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _yearController,
                  decoration: const InputDecoration(
                    labelText: 'سنة الصنع',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال سنة الصنع';
                    }
                    final year = int.tryParse(value);
                    if (year == null ||
                        year < 1900 ||
                        year > DateTime.now().year + 1) {
                      return 'يرجى إدخال سنة صحيحة';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _plateController,
                  decoration: const InputDecoration(
                    labelText: 'رقم اللوحة',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال رقم اللوحة';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<FuelType>(
                  value: _selectedFuelType,
                  decoration: const InputDecoration(
                    labelText: 'نوع الوقود',
                    border: OutlineInputBorder(),
                  ),
                  items: FuelType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(Vehicle(
                        id: '',
                        model: '',
                        year: 0,
                        plateNumber: '',
                        status: VehicleStatus.ACTIVE,
                        fuelType: type,
                        fuelLevel: 0,
                        insuranceExpiry: DateTime.now(),
                        createdAt: DateTime.now(),
                        updatedAt: DateTime.now(),
                      ).fuelTypeDisplayName),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedFuelType = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _fuelLevelController,
                  decoration: const InputDecoration(
                    labelText: 'مستوى الوقود (%)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال مستوى الوقود';
                    }
                    final level = double.tryParse(value);
                    if (level == null || level < 0 || level > 100) {
                      return 'يرجى إدخال قيمة بين 0 و 100';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: _isLoading ? null : _addVehicle,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFFF7E28),
              foregroundColor: Colors.white,
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('إضافة'),
          ),
        ],
      ),
    );
  }
}
