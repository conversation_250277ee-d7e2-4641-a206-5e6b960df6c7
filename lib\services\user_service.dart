import 'package:flutter/foundation.dart';
import '../models/user.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import 'api_error.dart';

class UserService {
  final ApiService _apiService = ApiService();

  // Set access token for authenticated requests
  void setAccessToken(String token) {
    _apiService.setAccessToken(token);
  }

  // Get current user profile
  Future<User> getUserProfile() async {
    if (kDebugMode) {
      print('👤 USER: Getting user profile');
    }

    try {
      final response = await _apiService.get('/api/users/profile');

      if (kDebugMode) {
        print('👤 USER: Profile response: $response');
      }

      final apiResponse = ApiResponse<User>.fromJson(
        response,
        (data) => User.fromJson(data as Map<String, dynamic>),
      );

      if (apiResponse.success && apiResponse.data != null) {
        return apiResponse.data!;
      } else {
        throw ApiException(ApiError(
          message: 'Failed to get user profile',
          details: apiResponse.error ?? apiResponse.message,
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('👤 USER: Error getting profile: $e');
      }
      rethrow;
    }
  }
}
