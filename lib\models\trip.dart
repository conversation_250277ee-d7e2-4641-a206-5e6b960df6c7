enum TripStatus { IN_PROGRESS, COMPLETED, CANCELLED }

class Trip {
  final String id;
  final String vehicleId;
  final String driverId;
  final String? startLocation;
  final String? endLocation;
  final double? startLatitude;
  final double? startLongitude;
  final double? endLatitude;
  final double? endLongitude;
  final DateTime startTime;
  final DateTime? endTime;
  final double? distance;
  final double? fuelConsumed;
  final TripStatus status;
  final DateTime updatedAt;

  Trip({
    required this.id,
    required this.vehicleId,
    required this.driverId,
    this.startLocation,
    this.endLocation,
    this.startLatitude,
    this.startLongitude,
    this.endLatitude,
    this.endLongitude,
    required this.startTime,
    this.endTime,
    this.distance,
    this.fuelConsumed,
    required this.status,
    required this.updatedAt,
  });

  factory Trip.fromJson(Map<String, dynamic> json) {
    return Trip(
      id: json['id'] as String,
      vehicleId: json['vehicleId'] as String,
      driverId: json['driverId'] as String,
      startLocation: json['startLocation'] as String?,
      endLocation: json['endLocation'] as String?,
      startLatitude: (json['startLatitude'] as num?)?.toDouble(),
      startLongitude: (json['startLongitude'] as num?)?.toDouble(),
      endLatitude: (json['endLatitude'] as num?)?.toDouble(),
      endLongitude: (json['endLongitude'] as num?)?.toDouble(),
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: json['endTime'] != null 
          ? DateTime.parse(json['endTime'] as String) 
          : null,
      distance: (json['distance'] as num?)?.toDouble(),
      fuelConsumed: (json['fuelConsumed'] as num?)?.toDouble(),
      status: TripStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => TripStatus.IN_PROGRESS,
      ),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'vehicleId': vehicleId,
      'driverId': driverId,
      'startLocation': startLocation,
      'endLocation': endLocation,
      'startLatitude': startLatitude,
      'startLongitude': startLongitude,
      'endLatitude': endLatitude,
      'endLongitude': endLongitude,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'distance': distance,
      'fuelConsumed': fuelConsumed,
      'status': status.name,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  Duration? get duration {
    if (endTime == null) return null;
    return endTime!.difference(startTime);
  }

  String get durationFormatted {
    final dur = duration;
    if (dur == null) return 'جاري...';
    
    final hours = dur.inHours;
    final minutes = dur.inMinutes % 60;
    
    if (hours > 0) {
      return '$hours ساعة و $minutes دقيقة';
    } else {
      return '$minutes دقيقة';
    }
  }

  String get distanceFormatted {
    if (distance == null) return 'غير محدد';
    return '${distance!.toStringAsFixed(1)} كم';
  }

  String get fuelConsumedFormatted {
    if (fuelConsumed == null) return 'غير محدد';
    return '${fuelConsumed!.toStringAsFixed(1)} لتر';
  }

  String get statusDisplayName {
    switch (status) {
      case TripStatus.IN_PROGRESS:
        return 'جاري';
      case TripStatus.COMPLETED:
        return 'مكتمل';
      case TripStatus.CANCELLED:
        return 'ملغي';
    }
  }

  @override
  String toString() {
    return 'Trip{id: $id, status: $status, startTime: $startTime, distance: $distance}';
  }
}
