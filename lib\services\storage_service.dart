import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _tokenExpiryKey = 'token_expiry';

  // Simple encryption key (in production, use a more secure method)
  static const String _encryptionKey = 'drivety_secure_key_2024';

  // Simple XOR encryption for basic security
  String _encrypt(String data) {
    final bytes = utf8.encode(data);
    final keyBytes = utf8.encode(_encryptionKey);
    final encrypted = <int>[];
    
    for (int i = 0; i < bytes.length; i++) {
      encrypted.add(bytes[i] ^ keyBytes[i % keyBytes.length]);
    }
    
    return base64Encode(encrypted);
  }

  // Simple XOR decryption
  String _decrypt(String encryptedData) {
    try {
      final encrypted = base64Decode(encryptedData);
      final keyBytes = utf8.encode(_encryptionKey);
      final decrypted = <int>[];
      
      for (int i = 0; i < encrypted.length; i++) {
        decrypted.add(encrypted[i] ^ keyBytes[i % keyBytes.length]);
      }
      
      return utf8.decode(decrypted);
    } catch (e) {
      if (kDebugMode) {
        print('💾 STORAGE: Decryption error: $e');
      }
      return '';
    }
  }

  // Write encrypted data
  Future<void> write(String key, String value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedValue = _encrypt(value);
      await prefs.setString(key, encryptedValue);
      
      if (kDebugMode) {
        print('💾 STORAGE: Stored $key successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('💾 STORAGE: Write error for $key: $e');
      }
      rethrow;
    }
  }

  // Read and decrypt data
  Future<String?> read(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final encryptedValue = prefs.getString(key);
      
      if (encryptedValue == null) {
        if (kDebugMode) {
          print('💾 STORAGE: No data found for $key');
        }
        return null;
      }
      
      final decryptedValue = _decrypt(encryptedValue);
      
      if (kDebugMode) {
        print('💾 STORAGE: Retrieved $key successfully');
      }
      
      return decryptedValue.isEmpty ? null : decryptedValue;
    } catch (e) {
      if (kDebugMode) {
        print('💾 STORAGE: Read error for $key: $e');
      }
      return null;
    }
  }

  // Delete data
  Future<void> delete(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(key);
      
      if (kDebugMode) {
        print('💾 STORAGE: Deleted $key successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('💾 STORAGE: Delete error for $key: $e');
      }
      rethrow;
    }
  }

  // Token-specific methods
  Future<void> storeAccessToken(String token) async {
    await write(_accessTokenKey, token);
  }

  Future<String?> getAccessToken() async {
    return await read(_accessTokenKey);
  }

  Future<void> storeRefreshToken(String token) async {
    await write(_refreshTokenKey, token);
  }

  Future<String?> getRefreshToken() async {
    return await read(_refreshTokenKey);
  }

  Future<void> storeTokenExpiry(String expiry) async {
    await write(_tokenExpiryKey, expiry);
  }

  Future<String?> getTokenExpiry() async {
    return await read(_tokenExpiryKey);
  }

  // Clear all tokens
  Future<void> clearAllTokens() async {
    await Future.wait([
      delete(_accessTokenKey),
      delete(_refreshTokenKey),
      delete(_tokenExpiryKey),
    ]);
    
    if (kDebugMode) {
      print('💾 STORAGE: All tokens cleared');
    }
  }
}
