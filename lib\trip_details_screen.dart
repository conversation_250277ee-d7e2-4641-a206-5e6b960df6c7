import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:intl/intl.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:ui' as ui;
import 'models/trip.dart';
import 'models/driver.dart';
import 'models/vehicle.dart';
import 'services/trip_service.dart';
import 'services/driver_service.dart';
import 'services/vehicle_service.dart';
import 'services/auth_service.dart';
import 'package:geocoding/geocoding.dart';
import 'utils/app_theme.dart';
import 'widgets/open_street_map.dart';

class TripDetailsScreen extends StatefulWidget {
  final Trip trip;

  const TripDetailsScreen({
    super.key,
    required this.trip,
  });

  @override
  State<TripDetailsScreen> createState() => _TripDetailsScreenState();
}

class _TripDetailsScreenState extends State<TripDetailsScreen> {
  final TripService _tripService = TripService();
  final DriverService _driverService = DriverService();
  final VehicleService _vehicleService = VehicleService();
  final AuthService _authService = AuthService();

  Driver? _driver;
  Vehicle? _vehicle;
  bool _isLoading = true;
  String? _errorMessage;
  String? _startAddress;
  String? _endAddress;
  bool _isEndingTrip = false;

  @override
  void initState() {
    super.initState();
    _loadTripDetails();
    _fetchAddressesIfNeeded();
  }

  Future<void> _loadTripDetails() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Get access token
      final accessToken = await _authService.getAccessToken();
      if (accessToken == null) {
        throw Exception('No access token found');
      }

      _driverService.setAccessToken(accessToken);
      _vehicleService.setAccessToken(accessToken);
      _tripService.setAccessToken(accessToken); // Set access token for trip service

      // Load driver and vehicle details
      _driver = await _driverService.getCurrentUserDriver();
      _vehicle = await _vehicleService.getVehicleById(widget.trip.vehicleId);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _fetchAddressesIfNeeded() async {
    if (widget.trip.startLocation == null &&
        widget.trip.startLatitude != null &&
        widget.trip.startLongitude != null) {
      try {
        List<Placemark> placemarks = await placemarkFromCoordinates(
          widget.trip.startLatitude!,
          widget.trip.startLongitude!,
        );
        if (placemarks.isNotEmpty) {
          final p = placemarks.first;
          setState(() {
            _startAddress = _formatPlacemark(p);
          });
        }
      } catch (e) {
        setState(() {
          _startAddress = 'عنوان البدء غير معروف';
        });
      }
    }
    if (widget.trip.endLocation == null &&
        widget.trip.endLatitude != null &&
        widget.trip.endLongitude != null) {
      try {
        List<Placemark> placemarks = await placemarkFromCoordinates(
          widget.trip.endLatitude!,
          widget.trip.endLongitude!,
        );
        if (placemarks.isNotEmpty) {
          final p = placemarks.first;
          setState(() {
            _endAddress = _formatPlacemark(p);
          });
        }
      } catch (e) {
        setState(() {
          _endAddress = 'عنوان النهاية غير معروف';
        });
      }
    }
  }

  String _formatPlacemark(Placemark p) {
    // Compose a readable address
    return [
      if (p.street != null && p.street!.isNotEmpty) p.street,
      if (p.subLocality != null && p.subLocality!.isNotEmpty) p.subLocality,
      if (p.locality != null && p.locality!.isNotEmpty) p.locality,
      if (p.administrativeArea != null && p.administrativeArea!.isNotEmpty) p.administrativeArea,
      if (p.country != null && p.country!.isNotEmpty) p.country,
    ].whereType<String>().join(', ');
  }

  Future<void> _endTrip() async {
    setState(() {
      _isEndingTrip = true;
    });
    try {
      await _tripService.endTrip(
        tripId: widget.trip.id,
        endLocation: _endAddress ?? '',
        endLatitude: widget.trip.endLatitude ?? 0.0,
        endLongitude: widget.trip.endLongitude ?? 0.0,
      );
      // Optionally refresh trip details or navigate back
      Navigator.of(context).pop(); // Go back after ending trip
    } catch (e) {
      // Handle error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to end trip: ${e.toString()}'),
          backgroundColor: Colors.redAccent,
        ),
      );
    } finally {
      setState(() {
        _isEndingTrip = false;
      });
    }
  }

   void _showEndTripDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Directionality(
          textDirection: ui.TextDirection.rtl,
          child: AlertDialog(
            title: const Text('إنهاء الرحلة'),
            content: const Text('هل أنت متأكد أنك تريد إنهاء هذه الرحلة؟'),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _endTrip();
                },
                 style: ElevatedButton.styleFrom(
                   backgroundColor: AppTheme.accentRed,
                   foregroundColor: Colors.white,
                 ),
                child: const Text('إنهاء'),
              ),
            ],
          ),
        );
      },
    );
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      body: SafeArea(
        child: Directionality(
          textDirection: ui.TextDirection.rtl,
          child: _buildContent(),
        ),
      ),
    );
  }

  Widget _buildHeaderContent() {
    final dateFormatter = DateFormat('EEEE، d MMMM yyyy', 'ar');
    final timeFormatter = DateFormat('HH:mm', 'ar');

    return Container(
      padding: const EdgeInsets.all(24), // Consistent padding
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Trip Status Badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8), // Consistent padding
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(25), // Consistent border radius
              border: Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  widget.trip.status == TripStatus.COMPLETED
                      ? Icons.check_circle_outline
                      : widget.trip.status == TripStatus.IN_PROGRESS
                          ? Icons.play_circle_outline
                          : Icons.cancel_outlined,
                  color: Colors.white,
                  size: 20, // Consistent icon size
                ),
                const SizedBox(width: 8), // Consistent spacing
                Text(
                  widget.trip.statusDisplayName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16, // Consistent font size
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12), // Consistent spacing
          // Date and Time
          Text(
            dateFormatter.format(widget.trip.startTime),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16, // Consistent font size
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            'بدأت في: ${timeFormatter.format(widget.trip.startTime)}',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 14, // Consistent font size
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryColor,
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24), // Consistent padding
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: AppTheme.accentRed,
                size: 48, // Consistent icon size
              ),
              const SizedBox(height: 16), // Consistent spacing
              Text(
                'خطأ في تحميل تفاصيل الرحلة',
                style: AppTheme.getTitleStyle().copyWith(fontSize: 20), // Use AppTheme style
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8), // Consistent spacing
              Text(
                _errorMessage!,
                style: AppTheme.getBodyStyle(), // Use AppTheme style
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24), // Consistent spacing
              ElevatedButton(
                onPressed: _loadTripDetails,
                style: AppTheme.getPrimaryButtonStyle(), // Use AppTheme style
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    // Trip details content
    return Column(
      children: [
        // Header with map
        Container(
          height: 200,
          decoration: BoxDecoration(
            gradient: AppTheme.getPrimaryGradient(),
            borderRadius: const BorderRadius.vertical(bottom: Radius.circular(24)),
            boxShadow: [AppTheme.getCardShadow()[0]],
          ),
          child: Stack(
            children: [
              // Map background
              Positioned.fill(
                child: OpenStreetMapWidget(
                  initialLatitude: widget.trip.startLatitude ?? 24.7136,
                  initialLongitude: widget.trip.startLongitude ?? 46.6753,
                  zoom: 12.0,
                  markers: [
                    if (widget.trip.startLatitude != null && widget.trip.startLongitude != null)
                      MapMarker(
                        latitude: widget.trip.startLatitude!,
                        longitude: widget.trip.startLongitude!,
                        icon: Icons.location_on,
                        color: AppTheme.accentGreen,
                      ),
                    if (widget.trip.endLatitude != null && widget.trip.endLongitude != null)
                      MapMarker(
                        latitude: widget.trip.endLatitude!,
                        longitude: widget.trip.endLongitude!,
                        icon: Icons.location_on,
                        color: AppTheme.accentRed,
                      ),
                  ],
                  route: null,
                  height: 200,
                  showAttribution: false,
                  interactive: false,
                ),
              ),
              // Gradient overlay
               Container(
                 decoration: BoxDecoration(
                   gradient: LinearGradient(
                     begin: Alignment.topCenter,
                     end: Alignment.bottomCenter,
                     colors: [ AppTheme.primaryColor.withOpacity(0.8), AppTheme.primaryColor.withOpacity(0.2)],
                   ),
                    borderRadius: const BorderRadius.vertical(bottom: Radius.circular(24)),
                 ),
               ),
              // Header content
              Align(alignment: Alignment.bottomRight, child: _buildHeaderContent()),
               // Back Button and Action Button
               Positioned(
                 top: 8,
                 right: 8,
                 left: 8,
                 child: SafeArea(
                   child: Row(
                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                     children: [
                       IconButton(
                         onPressed: () => Navigator.pop(context),
                         icon: Container(
                           padding: const EdgeInsets.all(AppTheme.spacingSmall),
                           decoration: AppTheme.getCardDecoration().copyWith(color: Colors.white.withOpacity(0.3)),
                           child: const Icon(Icons.arrow_back_ios_new, color: Colors.white, size: AppTheme.iconSizeSmall),
                         ),
                       ),
                       if (widget.trip.status == TripStatus.IN_PROGRESS)
                         ElevatedButton.icon(
                           onPressed: _isEndingTrip ? null : _showEndTripDialog,
                           icon: _isEndingTrip
                               ? const SizedBox(width: 18, height: 18, child: CircularProgressIndicator(strokeWidth: 2, valueColor: AlwaysStoppedAnimation<Color>(Colors.white)))
                               : const Icon(Icons.stop, size: 18),
                           label: const Text('إنهاء الرحلة'),
                           style: ElevatedButton.styleFrom(
                             backgroundColor: AppTheme.accentRed,
                             foregroundColor: Colors.white,
                             elevation: 0,
                             shape: RoundedRectangleBorder(
                               borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                             ),
                           ),
                         ),
                     ],
                   ),
                 ),
               ),
            ],
          ),
        ),

        // Trip Details Cards
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16), // Consistent padding
            child: Column(
              children: [
                // Distance and Duration
                _buildDetailCard(
                  children: [
                    _buildDetailRow(
                      icon: Icons.route_outlined,
                      label: 'المسافة المقطوعة',
                      value: _formatDistance(widget.trip.distance),
                      iconColor: AppTheme.primaryColor,
                    ),
                    const SizedBox(height: 16), // Consistent spacing
                    _buildDetailRow(
                      icon: Icons.timer_outlined,
                      label: 'مدة الرحلة',
                      value: _formatDuration(widget.trip.duration),
                      iconColor: AppTheme.secondaryColor,
                    ),
                  ],
                ),
                const SizedBox(height: 16), // Consistent spacing

                // Speed and Fuel
                 _buildDetailCard(
                   children: [
                      _buildDetailRow(
                       icon: Icons.speed_outlined,
                       label: 'متوسط السرعة',
                       value: _formatSpeed(widget.trip.distance != null && widget.trip.duration != null 
                           ? (widget.trip.distance! / widget.trip.duration!.inHours)
                           : null),
                       iconColor: AppTheme.accentBlue,
                     ),
                     const SizedBox(height: 16), // Consistent spacing
                     _buildDetailRow(
                       icon: Icons.local_gas_station_outlined,
                       label: 'الوقود المستهلك',
                       value: _formatFuelConsumption(widget.trip.fuelConsumed),
                        iconColor: AppTheme.accentGreen,
                     ),
                   ],
                 ),
                const SizedBox(height: 16), // Consistent spacing

                // Locations
                _buildDetailCard(
                  children: [
                    _buildDetailRow(
                      icon: Icons.location_on_outlined,
                      label: 'نقطة البداية',
                      value: _startAddress ?? widget.trip.startLocation ?? 'عنوان غير متوفر',
                       iconColor: AppTheme.accentGreen,
                    ),
                    const SizedBox(height: 16), // Consistent spacing
                    _buildDetailRow(
                      icon: Icons.location_on_outlined,
                      label: 'نقطة النهاية',
                      value: _endAddress ?? widget.trip.endLocation ?? 'عنوان غير متوفر',
                       iconColor: AppTheme.accentRed,
                    ),
                  ],
                ),
                 const SizedBox(height: 16), // Consistent spacing

                // Vehicle and Driver Info
                if (_vehicle != null)
                  _buildDetailCard(
                     children: [
                       _buildDetailRow(
                         icon: Icons.directions_car_outlined,
                         label: 'المركبة',
                         value: '${_vehicle!.model} (${_vehicle!.plateNumber})',
                          iconColor: AppTheme.primaryColor,
                       ),
                        const SizedBox(height: 16), // Consistent spacing
                       _buildDetailRow(
                          icon: Icons.person_outline,
                          label: 'السائق',
                           value: _driver?.name ?? 'غير متوفر',
                            iconColor: AppTheme.secondaryColor,
                       ),
                     ],
                   ),
                  const SizedBox(height: 16), // Consistent spacing

              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailCard({
    required List<Widget> children,
  }) {
    return Container(
      padding: const EdgeInsets.all(16), // Consistent padding
      decoration: AppTheme.getCardDecoration(), // Consistent decoration
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String label,
    required String value,
    required Color iconColor,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: iconColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: iconColor, size: 20),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            label,
            style: AppTheme.getBodyStyle(),
          ),
        ),
        Text(
          value,
          style: AppTheme.getSubtitleStyle(),
        ),
      ],
    );
  }

  String _formatDuration(Duration? duration) {
    if (duration == null) return '00:00:00';
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }

  // Helper method to format distance
  String _formatDistance(double? distance) {
    if (distance == null) return '0.0 كم';
    return '${distance.toStringAsFixed(1)} كم';
  }

  // Helper method to format fuel consumption
  String _formatFuelConsumption(double? fuel) {
    if (fuel == null) return '0.0 لتر';
    return '${fuel.toStringAsFixed(1)} لتر';
  }

  // Helper method to format speed
  String _formatSpeed(double? speed) {
    if (speed == null) return '0.0 كم/ساعة';
    return '${speed.toStringAsFixed(1)} كم/ساعة';
  }
}
