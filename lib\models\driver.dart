import 'vehicle.dart';

class Driver {
  final String id;
  final String userId;
  final String name;
  final String licenseNumber;
  final DateTime licenseExpiry;
  final String phoneNumber;
  final double driverScore;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<Vehicle> vehicles;

  Driver({
    required this.id,
    required this.userId,
    required this.name,
    required this.licenseNumber,
    required this.licenseExpiry,
    required this.phoneNumber,
    required this.driverScore,
    required this.createdAt,
    required this.updatedAt,
    required this.vehicles,
  });

  factory Driver.fromJson(Map<String, dynamic> json) {
    return Driver(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      licenseNumber: json['licenseNumber'] as String,
      licenseExpiry: DateTime.parse(json['licenseExpiry'] as String),
      phoneNumber: json['phoneNumber'] as String,
      driverScore: (json['driverScore'] as num).toDouble(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      vehicles: (json['vehicles'] as List<dynamic>?)
              ?.map((v) => Vehicle.fromJson(v as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'name': name,
      'licenseNumber': licenseNumber,
      'licenseExpiry': licenseExpiry.toIso8601String(),
      'phoneNumber': phoneNumber,
      'driverScore': driverScore,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'vehicles': vehicles.map((v) => v.toJson()).toList(),
    };
  }

  String get scoreGrade {
    if (driverScore >= 90) return 'ممتاز';
    if (driverScore >= 80) return 'جيد جداً';
    if (driverScore >= 70) return 'جيد';
    if (driverScore >= 60) return 'مقبول';
    return 'ضعيف';
  }

  bool get isLicenseExpiringSoon {
    final now = DateTime.now();
    final daysUntilExpiry = licenseExpiry.difference(now).inDays;
    return daysUntilExpiry <= 30; // License expires in 30 days or less
  }

  @override
  String toString() {
    return 'Driver{id: $id, name: $name, licenseNumber: $licenseNumber, score: $driverScore}';
  }
}
