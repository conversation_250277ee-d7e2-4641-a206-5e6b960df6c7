import 'package:flutter/material.dart';
import 'utils/responsive_size.dart';
import 'utils/app_theme.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      body: SafeArea(
        child: Directionality(
          textDirection: TextDirection.rtl,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                // Enhanced Header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: Container(
                        padding: const EdgeInsets.all(AppTheme.spacingSmall),
                        decoration: AppTheme.getCardDecoration(),
                        child: Icon(Icons.arrow_back_ios_new, 
                          color: AppTheme.primaryColor,
                          size: AppTheme.iconSizeSmall,
                        ),
                      ),
                    ),
                    Text(
                      'الإعدادات',
                      style: AppTheme.getTitleStyle(),
                    ),
                    const SizedBox(width: 40), // For balance
                  ],
                ),
                const SizedBox(height: 24),
                Expanded(
                  child: ListView(
                    children: [
                      _buildSettingsSection(
                        title: 'الحساب',
                        items: [
                          _buildSettingsItem(
                            icon: Icons.person_outline,
                            title: 'الملف الشخصي',
                            onTap: () {
                              // Handle profile tap
                            },
                          ),
                          _buildSettingsItem(
                            icon: Icons.lock_outline,
                            title: 'تغيير كلمة المرور',
                            onTap: () {
                              // Handle password change tap
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      _buildSettingsSection(
                        title: 'التطبيق',
                        items: [
                          _buildSettingsItem(
                            icon: Icons.language,
                            title: 'اللغة',
                            onTap: () {
                              // Handle language tap
                            },
                          ),
                          _buildSettingsItem(
                            icon: Icons.notifications_outlined,
                            title: 'الإشعارات',
                            onTap: () {
                              // Handle notifications tap
                            },
                          ),
                          _buildSettingsItem(
                            icon: Icons.dark_mode_outlined,
                            title: 'المظهر',
                            onTap: () {
                              // Handle theme tap
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      _buildSettingsSection(
                        title: 'المساعدة والدعم',
                        items: [
                          _buildSettingsItem(
                            icon: Icons.help_outline,
                            title: 'المساعدة',
                            onTap: () {
                              // Handle help tap
                            },
                          ),
                          _buildSettingsItem(
                            icon: Icons.info_outline,
                            title: 'عن التطبيق',
                            onTap: () {
                              // Handle about tap
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      _buildSettingsItem(
                        icon: Icons.logout,
                        title: 'تسجيل الخروج',
                        textColor: AppTheme.accentRed,
                        onTap: () {
                          // Handle logout tap
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required List<Widget> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDark,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: items,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Colors.grey.withOpacity(0.1),
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: (textColor ?? AppTheme.primaryColor).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: textColor ?? AppTheme.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    color: textColor ?? AppTheme.textDark,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: textColor ?? AppTheme.textMedium,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
