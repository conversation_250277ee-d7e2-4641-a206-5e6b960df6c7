import 'package:flutter/material.dart';

/// App theme class that contains all the styling for the app
class AppTheme {
  // Primary colors
  static const Color primaryColor = Color(0xFFFFA424);
  static const Color primaryLightColor = Color(0xFFFFF0E8);
  static const Color secondaryColor = Color(0xFF4A80F0);
  static const Color secondaryLightColor = Color(0xFFEEF2FF);

  // Accent colors
  static const Color accentGreen = Color(0xFF4CAF50);
  static const Color accentGreenLight = Color(0xFFE8F5E9);
  static const Color accentGreenDark = Color(0xFF2E7D32);
  static const Color accentRed = Color(0xFFE53935);
  static const Color accentRedLight = Color(0xFFFFEBEE);
  static const Color accentYellow = Color(0xFFFFC107);
  static const Color accentYellowLight = Color(0xFFFFF8E1);
  static const Color accentYellowDark = Color(0xFFF57F17);
  static const Color accentBlue = Color(0xFF2196F3);
  static const Color accentBlueLight = Color(0xFFE3F2FD);

  // Background colors
  static const Color backgroundLight = Color(0xFFF8F9FA);
  static const Color backgroundWhite = Colors.white;
  static const Color backgroundDark = Color(0xFF121212);
  static const Color backgroundCard = Color(0xFF1E1E1E);

  // Text colors
  static const Color textDark = Color(0xFF212121);
  static const Color textMedium = Color(0xFF757575);
  static const Color textLight = Color(0xFFBDBDBD);

  // Border radius values
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;
  static const double borderRadiusXLarge = 24.0;

  // Spacing values
  static const double spacingXSmall = 4.0;
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 16.0;
  static const double spacingLarge = 24.0;
  static const double spacingXLarge = 32.0;

  // Icon sizes
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;

  // Font sizes
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 20.0;
  static const double fontSizeXXLarge = 24.0;

  // Get primary gradient
  static LinearGradient getPrimaryGradient() {
    return const LinearGradient(
      colors: [primaryColor, Color(0xFFFFB74D)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  // Get card shadow
  static List<BoxShadow> getCardShadow() {
    return [
      BoxShadow(
        color: Colors.black.withOpacity(0.05),
        blurRadius: 10,
        offset: const Offset(0, 4),
      ),
    ];
  }

  // Get button shadow
  static List<BoxShadow> getButtonShadow() {
    return [
      BoxShadow(
        color: primaryColor.withOpacity(0.3),
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ];
  }

  // Common button styles
  static ButtonStyle getPrimaryButtonStyle() {
    return ElevatedButton.styleFrom(
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: spacingLarge,
        vertical: spacingMedium,
      ),
    );
  }

  static ButtonStyle getSecondaryButtonStyle() {
    return ElevatedButton.styleFrom(
      backgroundColor: Colors.white,
      foregroundColor: primaryColor,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        side: const BorderSide(color: primaryColor),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: spacingLarge,
        vertical: spacingMedium,
      ),
    );
  }

  static ButtonStyle getTextButtonStyle() {
    return TextButton.styleFrom(
      foregroundColor: primaryColor,
      padding: const EdgeInsets.symmetric(
        horizontal: spacingMedium,
        vertical: spacingSmall,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
      ),
    );
  }

  // Common container decoration
  static BoxDecoration getCardDecoration() {
    return BoxDecoration(
      color: backgroundWhite,
      borderRadius: BorderRadius.circular(borderRadiusMedium),
      boxShadow: getCardShadow(),
    );
  }

  static BoxDecoration getIconContainerDecoration() {
    return BoxDecoration(
      color: primaryLightColor,
      borderRadius: BorderRadius.circular(borderRadiusMedium),
    );
  }

  // Common text styles
  static TextStyle getTitleStyle() {
    return const TextStyle(
      fontSize: fontSizeXXLarge,
      fontWeight: FontWeight.bold,
      color: textDark,
    );
  }

  static TextStyle getSubtitleStyle() {
    return const TextStyle(
      fontSize: fontSizeLarge,
      fontWeight: FontWeight.w600,
      color: textDark,
    );
  }

  static TextStyle getBodyStyle() {
    return const TextStyle(
      fontSize: fontSizeMedium,
      color: textMedium,
    );
  }

  // Light theme data
  static ThemeData getLightTheme() {
    return ThemeData(
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundLight,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        surface: backgroundLight,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: backgroundWhite,
        foregroundColor: textDark,
        elevation: 0,
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(color: textDark),
        displayMedium: TextStyle(color: textDark),
        displaySmall: TextStyle(color: textDark),
        headlineMedium: TextStyle(color: textDark),
        headlineSmall: TextStyle(color: textDark),
        titleLarge: TextStyle(color: textDark),
        titleMedium: TextStyle(color: textDark),
        titleSmall: TextStyle(color: textDark),
        bodyLarge: TextStyle(color: textDark),
        bodyMedium: TextStyle(color: textDark),
        bodySmall: TextStyle(color: textMedium),
        labelLarge: TextStyle(color: textDark),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: getPrimaryButtonStyle(),
      ),
      textButtonTheme: TextButtonThemeData(
        style: getTextButtonStyle(),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: getSecondaryButtonStyle(),
      ),
      cardTheme: CardThemeData(
        color: backgroundWhite,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: backgroundLight,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: primaryColor),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
          borderSide: const BorderSide(color: accentRed),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacingMedium,
          vertical: spacingMedium,
        ),
      ),
    );
  }
}
