import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:ui' as ui;
import 'models/trip.dart';
import 'models/driver.dart';
import 'services/trip_service.dart';
import 'services/driver_service.dart';
import 'services/auth_service.dart';
import 'trip_details_screen.dart';
import 'trip_management_screen.dart';
import 'utils/app_theme.dart';

class TripsScreen extends StatefulWidget {
  const TripsScreen({super.key});

  @override
  State<TripsScreen> createState() => _TripsScreenState();
}

class _TripsScreenState extends State<TripsScreen> {
  final List<String> _filterOptions = ['الكل', 'اليوم', 'الأسبوع', 'الشهر'];
  String _selectedFilter = 'الكل';

  final TripService _tripService = TripService();
  final DriverService _driverService = DriverService();
  final AuthService _authService = AuthService();

  List<Trip> _trips = [];
  Driver? _currentDriver;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Get access token and set it for services
      final accessToken = await _authService.getAccessToken();
      if (accessToken == null) {
        throw Exception('No access token found');
      }

      _tripService.setAccessToken(accessToken);
      _driverService.setAccessToken(accessToken);

      // Get current driver
      _currentDriver = await _driverService.getCurrentUserDriver();

      // Get trips for the driver
      _trips = await _tripService.getDriverTrips(_currentDriver!.id);

      // Sort trips by start time (newest first)
      _trips.sort((a, b) => b.startTime.compareTo(a.startTime));

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  List<Trip> get _filteredTrips {
    final now = DateTime.now();
    switch (_selectedFilter) {
      case 'اليوم':
        return _trips.where((trip) {
          return trip.startTime.year == now.year &&
              trip.startTime.month == now.month &&
              trip.startTime.day == now.day;
        }).toList();
      case 'الأسبوع':
        final weekStart = now.subtract(Duration(days: now.weekday - 1));
        return _trips.where((trip) {
          return trip.startTime.isAfter(weekStart);
        }).toList();
      case 'الشهر':
        return _trips.where((trip) {
          return trip.startTime.year == now.year &&
              trip.startTime.month == now.month;
        }).toList();
      default:
        return _trips;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Directionality(
          textDirection: ui.TextDirection.rtl,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                // Enhanced Header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: Container(
                        padding: const EdgeInsets.all(AppTheme.spacingSmall),
                        decoration: AppTheme.getCardDecoration(),
                        child: Icon(Icons.arrow_back_ios_new, 
                          color: AppTheme.primaryColor,
                          size: AppTheme.iconSizeSmall,
                        ),
                      ),
                    ),
                    Text(
                      'الرحلات',
                      style: AppTheme.getTitleStyle(),
                    ),
                    const SizedBox(width: 40), // For balance
                  ],
                ),

                const SizedBox(height: 24),

                // Enhanced Search Bar
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.search, color: Colors.grey[400]),
                      const SizedBox(width: 12),
                      Expanded(
                        child: TextField(
                          decoration: InputDecoration(
                            hintText: 'بحث عن رحلة...',
                            border: InputBorder.none,
                            hintStyle: TextStyle(
                              color: Colors.grey[400],
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Enhanced Filter Options
                SizedBox(
                  height: 45,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _filterOptions.length,
                    itemBuilder: (context, index) {
                      final option = _filterOptions[index];
                      final isSelected = option == _selectedFilter;

                      return Padding(
                        padding: const EdgeInsets.only(left: 8),
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              _selectedFilter = option;
                            });
                          },
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 20, vertical: 10),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? const Color(0xFFFFA424)
                                  : Colors.white,
                              borderRadius: BorderRadius.circular(25),
                              boxShadow: [
                                BoxShadow(
                                  color: isSelected
                                      ? const Color(0xFFFFA424).withOpacity(0.3)
                                      : Colors.black.withOpacity(0.05),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Text(
                              option,
                              style: TextStyle(
                                color: isSelected
                                    ? Colors.white
                                    : Colors.grey[700],
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.w500,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),

                const SizedBox(height: 24),

                // Trips List
                Expanded(
                  child: _buildTripsContent(),
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const TripManagementScreen(),
            ),
          );
        },
        backgroundColor: const Color(0xFFFFA424),
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add_road),
        label: const Text(
          'رحلة جديدة',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 4,
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  Widget _buildTripsContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Color(0xFFFFA424),
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل الرحلات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFFA424),
                foregroundColor: Colors.white,
              ),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    final filteredTrips = _filteredTrips;
    if (filteredTrips.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.route_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد رحلات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'لم يتم العثور على رحلات للفترة المحددة',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      color: const Color(0xFFFF7E28),
      child: ListView.builder(
        itemCount: filteredTrips.length,
        itemBuilder: (context, index) {
          return _buildTripItem(filteredTrips[index]);
        },
      ),
    );
  }

  Widget _buildTripItem(Trip trip) {
    final dateFormatter = DateFormat('d MMMM yyyy', 'ar');
    final timeFormatter = DateFormat('HH:mm', 'ar');

    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => TripDetailsScreen(trip: trip),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Enhanced Trip Map
            Container(
              height: 140,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                child: _buildTripMap(trip),
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Date and Status Row
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            dateFormatter.format(trip.startTime),
                            style: AppTheme.getSubtitleStyle(),
                          ),
                          Text(
                            timeFormatter.format(trip.startTime),
                            style: AppTheme.getBodyStyle(),
                          ),
                        ],
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppTheme.spacingMedium,
                          vertical: AppTheme.spacingSmall,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryLightColor,
                          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
                        ),
                        child: Text(
                          trip.statusDisplayName,
                          style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontSize: AppTheme.fontSizeMedium,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // From and To with enhanced styling
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundLight,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 48,
                          margin: const EdgeInsets.only(left: 8),
                          child: Column(
                            children: [
                              Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: AppTheme.primaryColor,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: AppTheme.backgroundWhite, width: 2),
                                ),
                              ),
                              Expanded(
                                child: Container(
                                  width: 2,
                                  color: AppTheme.textLight.withOpacity(0.5),
                                ),
                              ),
                              Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: AppTheme.primaryColor.withOpacity(0.7),
                                  shape: BoxShape.circle,
                                  border: Border.all(color: AppTheme.backgroundWhite, width: 2),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'من: ${trip.startLocation ?? 'غير محدد'}',
                                style: AppTheme.getBodyStyle(),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'إلى: ${trip.endLocation ?? 'غير محدد'}',
                                style: AppTheme.getBodyStyle(),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Enhanced Trip Details
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundLight,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildTripDetail(
                            Icons.straighten, trip.distanceFormatted, 'المسافة'),
                        _buildTripDetail(
                            Icons.access_time, trip.durationFormatted, 'المدة'),
                        _buildTripDetail(Icons.local_gas_station,
                            trip.fuelConsumedFormatted, 'استهلاك الوقود'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTripDetail(IconData icon, String value, String label) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppTheme.primaryLightColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: AppTheme.primaryColor, size: 20),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: AppTheme.fontSizeMedium,
            color: AppTheme.textDark,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: AppTheme.textMedium,
            fontSize: AppTheme.fontSizeSmall,
          ),
        ),
      ],
    );
  }

  Widget _buildTripMap(Trip trip) {
    // Check if we have coordinates
    if (trip.startLatitude == null || trip.startLongitude == null) {
      return Container(
        color: Colors.grey[200],
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.location_off, size: 24, color: Colors.grey),
              SizedBox(height: 4),
              Text(
                'لا توجد بيانات موقع',
                style: TextStyle(color: Colors.grey, fontSize: 12),
              ),
            ],
          ),
        ),
      );
    }

    final startPoint = LatLng(trip.startLatitude!, trip.startLongitude!);
    LatLng? endPoint;
    if (trip.endLatitude != null && trip.endLongitude != null) {
      endPoint = LatLng(trip.endLatitude!, trip.endLongitude!);
    }

    // Calculate center and zoom
    LatLng center = startPoint;
    double zoom = 14.0;

    if (endPoint != null) {
      // Calculate center between start and end points
      center = LatLng(
        (startPoint.latitude + endPoint.latitude) / 2,
        (startPoint.longitude + endPoint.longitude) / 2,
      );

      // Calculate appropriate zoom level based on distance
      final distance =
          const Distance().as(LengthUnit.Kilometer, startPoint, endPoint);
      if (distance > 10) {
        zoom = 11.0;
      } else if (distance > 5) {
        zoom = 12.0;
      } else if (distance > 1) {
        zoom = 13.0;
      }
    }

    return FlutterMap(
      options: MapOptions(
        initialCenter: center,
        initialZoom: zoom,
        interactionOptions: const InteractionOptions(
          flags: InteractiveFlag.none, // Disable interaction in list view
        ),
      ),
      children: [
        TileLayer(
          urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
          userAgentPackageName: 'com.example.drivety',
        ),
        MarkerLayer(
          markers: [
            // Start marker
            Marker(
              point: startPoint,
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor,
                  shape: BoxShape.circle,
                  border: Border.all(color: AppTheme.backgroundWhite, width: 3),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor.withOpacity(0.5),
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 14,
                ),
              ),
            ),
            // End marker (if exists)
            if (endPoint != null)
              Marker(
                point: endPoint,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.7),
                    shape: BoxShape.circle,
                    border: Border.all(color: AppTheme.backgroundWhite, width: 3),
                     boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor.withOpacity(0.3),
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                  ),
                  child: const Icon(
                    Icons.stop,
                    color: Colors.white,
                    size: 14,
                  ),
                ),
              ),
          ],
        ),
        // Draw line between start and end if both exist
        if (endPoint != null)
          PolylineLayer(
            polylines: [
              Polyline(
                points: [startPoint, endPoint],
                strokeWidth: 3.0,
                color: AppTheme.primaryColor,
              ),
            ],
          ),
      ],
    );
  }
}
