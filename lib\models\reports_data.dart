enum ReportPeriod {
  daily,
  weekly,
  monthly,
  yearly,
}

extension ReportPeriodExtension on ReportPeriod {
  String get displayName {
    switch (this) {
      case ReportPeriod.daily:
        return 'يومي';
      case ReportPeriod.weekly:
        return 'أسبوعي';
      case ReportPeriod.monthly:
        return 'شهري';
      case ReportPeriod.yearly:
        return 'سنوي';
    }
  }

  String get name {
    switch (this) {
      case ReportPeriod.daily:
        return 'daily';
      case ReportPeriod.weekly:
        return 'weekly';
      case ReportPeriod.monthly:
        return 'monthly';
      case ReportPeriod.yearly:
        return 'yearly';
    }
  }
}

class ReportsData {
  final String driverId;
  final ReportPeriod period;
  final DateTime periodStart;
  final DateTime periodEnd;
  final int totalTrips;
  final double totalDistance;
  final double totalFuel;
  final Duration totalDuration;
  final double avgFuelEfficiency;
  final double avgSpeed;
  final double safetyScore;
  final List<DailyData> dailyData;
  final List<FuelData> fuelData;
  final List<PerformanceData> performanceData;
  final DateTime lastUpdated;

  const ReportsData({
    required this.driverId,
    required this.period,
    required this.periodStart,
    required this.periodEnd,
    required this.totalTrips,
    required this.totalDistance,
    required this.totalFuel,
    required this.totalDuration,
    required this.avgFuelEfficiency,
    required this.avgSpeed,
    required this.safetyScore,
    required this.dailyData,
    required this.fuelData,
    required this.performanceData,
    required this.lastUpdated,
  });

  factory ReportsData.fromJson(Map<String, dynamic> json) {
    return ReportsData(
      driverId: json['driverId'] ?? '',
      period: ReportPeriod.values.firstWhere(
        (p) => p.name == json['period'],
        orElse: () => ReportPeriod.monthly,
      ),
      periodStart: DateTime.parse(json['periodStart']),
      periodEnd: DateTime.parse(json['periodEnd']),
      totalTrips: json['totalTrips'] ?? 0,
      totalDistance: (json['totalDistance'] ?? 0.0).toDouble(),
      totalFuel: (json['totalFuel'] ?? 0.0).toDouble(),
      totalDuration: Duration(seconds: json['totalDurationSeconds'] ?? 0),
      avgFuelEfficiency: (json['avgFuelEfficiency'] ?? 0.0).toDouble(),
      avgSpeed: (json['avgSpeed'] ?? 0.0).toDouble(),
      safetyScore: (json['safetyScore'] ?? 0.0).toDouble(),
      dailyData: (json['dailyData'] as List<dynamic>?)
              ?.map((item) => DailyData.fromJson(item))
              .toList() ??
          [],
      fuelData: (json['fuelData'] as List<dynamic>?)
              ?.map((item) => FuelData.fromJson(item))
              .toList() ??
          [],
      performanceData: (json['performanceData'] as List<dynamic>?)
              ?.map((item) => PerformanceData.fromJson(item))
              .toList() ??
          [],
      lastUpdated: DateTime.parse(json['lastUpdated']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'driverId': driverId,
      'period': period.name,
      'periodStart': periodStart.toIso8601String(),
      'periodEnd': periodEnd.toIso8601String(),
      'totalTrips': totalTrips,
      'totalDistance': totalDistance,
      'totalFuel': totalFuel,
      'totalDurationSeconds': totalDuration.inSeconds,
      'avgFuelEfficiency': avgFuelEfficiency,
      'avgSpeed': avgSpeed,
      'safetyScore': safetyScore,
      'dailyData': dailyData.map((item) => item.toJson()).toList(),
      'fuelData': fuelData.map((item) => item.toJson()).toList(),
      'performanceData': performanceData.map((item) => item.toJson()).toList(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  // Calculate fuel efficiency grade
  String get fuelEfficiencyGrade {
    if (avgFuelEfficiency >= 15) return 'ممتاز';
    if (avgFuelEfficiency >= 12) return 'جيد جداً';
    if (avgFuelEfficiency >= 10) return 'جيد';
    if (avgFuelEfficiency >= 8) return 'متوسط';
    return 'يحتاج تحسين';
  }

  // Calculate safety grade
  String get safetyGrade {
    if (safetyScore >= 90) return 'ممتاز';
    if (safetyScore >= 80) return 'جيد جداً';
    if (safetyScore >= 70) return 'جيد';
    if (safetyScore >= 60) return 'متوسط';
    return 'يحتاج تحسين';
  }

  // Calculate average trip distance
  double get avgTripDistance {
    return totalTrips > 0 ? totalDistance / totalTrips : 0.0;
  }

  // Calculate average trip duration
  Duration get avgTripDuration {
    return totalTrips > 0
        ? Duration(seconds: totalDuration.inSeconds ~/ totalTrips)
        : Duration.zero;
  }
}

class DailyData {
  final DateTime date;
  final int trips;
  final double distance;
  final double fuel;
  final Duration duration;

  const DailyData({
    required this.date,
    required this.trips,
    required this.distance,
    required this.fuel,
    required this.duration,
  });

  factory DailyData.fromJson(Map<String, dynamic> json) {
    return DailyData(
      date: DateTime.parse(json['date']),
      trips: json['trips'] ?? 0,
      distance: (json['distance'] ?? 0.0).toDouble(),
      fuel: (json['fuel'] ?? 0.0).toDouble(),
      duration: Duration(seconds: json['durationSeconds'] ?? 0),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'trips': trips,
      'distance': distance,
      'fuel': fuel,
      'durationSeconds': duration.inSeconds,
    };
  }
}

class FuelData {
  final DateTime date;
  final double consumed;
  final double efficiency;
  final double cost;

  const FuelData({
    required this.date,
    required this.consumed,
    required this.efficiency,
    required this.cost,
  });

  factory FuelData.fromJson(Map<String, dynamic> json) {
    return FuelData(
      date: DateTime.parse(json['date']),
      consumed: (json['consumed'] ?? 0.0).toDouble(),
      efficiency: (json['efficiency'] ?? 0.0).toDouble(),
      cost: (json['cost'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'consumed': consumed,
      'efficiency': efficiency,
      'cost': cost,
    };
  }
}

class PerformanceData {
  final DateTime date;
  final double safetyScore;
  final double avgSpeed;
  final double fuelEfficiency;
  final int hardBrakes;
  final int rapidAccelerations;

  const PerformanceData({
    required this.date,
    required this.safetyScore,
    required this.avgSpeed,
    required this.fuelEfficiency,
    required this.hardBrakes,
    required this.rapidAccelerations,
  });

  factory PerformanceData.fromJson(Map<String, dynamic> json) {
    return PerformanceData(
      date: DateTime.parse(json['date']),
      safetyScore: (json['safetyScore'] ?? 0.0).toDouble(),
      avgSpeed: (json['avgSpeed'] ?? 0.0).toDouble(),
      fuelEfficiency: (json['fuelEfficiency'] ?? 0.0).toDouble(),
      hardBrakes: json['hardBrakes'] ?? 0,
      rapidAccelerations: json['rapidAccelerations'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'safetyScore': safetyScore,
      'avgSpeed': avgSpeed,
      'fuelEfficiency': fuelEfficiency,
      'hardBrakes': hardBrakes,
      'rapidAccelerations': rapidAccelerations,
    };
  }
}

class ReportsSummary {
  final int totalTrips;
  final double totalDistance;
  final double totalFuel;
  final Duration totalDuration;
  final double avgFuelEfficiency;
  final double avgSpeed;
  final double safetyScore;

  const ReportsSummary({
    required this.totalTrips,
    required this.totalDistance,
    required this.totalFuel,
    required this.totalDuration,
    required this.avgFuelEfficiency,
    required this.avgSpeed,
    required this.safetyScore,
  });

  factory ReportsSummary.empty() {
    return const ReportsSummary(
      totalTrips: 0,
      totalDistance: 0.0,
      totalFuel: 0.0,
      totalDuration: Duration.zero,
      avgFuelEfficiency: 0.0,
      avgSpeed: 0.0,
      safetyScore: 0.0,
    );
  }
}
