import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'models/trip.dart';
import 'models/driver.dart';
import 'services/trip_service.dart';
import 'services/driver_service.dart';
import 'services/auth_service.dart';
import 'utils/app_theme.dart';
import 'package:intl/intl.dart';
import 'dart:ui' as ui;

class TripManagementScreen extends StatefulWidget {
  const TripManagementScreen({super.key});

  @override
  State<TripManagementScreen> createState() => _TripManagementScreenState();
}

class _TripManagementScreenState extends State<TripManagementScreen> {
  final TripService _tripService = TripService();
  final DriverService _driverService = DriverService();
  final AuthService _authService = AuthService();

  Driver? _currentDriver;
  Trip? _activeTrip;
  bool _isLoading = true;
  String? _errorMessage;
  Position? _currentPosition;
  bool _isStartingTrip = false;
  bool _isEndingTrip = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get access token and set it for services
      final accessToken = await _authService.getAccessToken();
      if (accessToken == null) {
        throw Exception('No access token found');
      }

      _tripService.setAccessToken(accessToken);
      _driverService.setAccessToken(accessToken);

      // Get current driver
      _currentDriver = await _driverService.getCurrentUserDriver();

      // Get current location
      await _getCurrentLocation();

      // Check for active trips
      if (_currentDriver != null) {
        final trips = await _tripService.getDriverTrips(_currentDriver!.id);
        try {
          _activeTrip = trips.firstWhere(
            (trip) => trip.status == TripStatus.IN_PROGRESS,
          );
        } catch (e) {
          _activeTrip = null; // No active trip found
        }
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = e.toString();
        });
      }
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('خدمة الموقع غير مفعلة');
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('تم رفض إذن الموقع');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('تم رفض إذن الموقع نهائياً');
      }

      _currentPosition = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
    } catch (e) {
      // print('Error getting location: $e');
       if(mounted) _showErrorSnackbar('لا يمكن الحصول على الموقع الحالي: ${e.toString()}');
    }
  }

  Future<void> _startTrip() async {
    if (_currentDriver == null || _currentDriver!.vehicles.isEmpty) {
      _showErrorSnackbar('لا توجد مركبات متاحة. يرجى إضافة مركبة أولاً.');
      return;
    }

    if (_currentPosition == null) {
      await _getCurrentLocation();
      if (_currentPosition == null) {
        // Error snackbar is shown in _getCurrentLocation
        return;
      }
    }

    setState(() {
      _isStartingTrip = true;
    });

    try {
      final trip = await _tripService.startTripWithRealLocation(
        driverId: _currentDriver!.id,
        vehicleId: _currentDriver!.vehicles.first.id,
        startLatitude: _currentPosition!.latitude,
        startLongitude: _currentPosition!.longitude,
      );

      if (mounted) {
        setState(() {
          _activeTrip = trip;
          _isStartingTrip = false;
        });
        _showSuccessSnackbar('تم بدء الرحلة بنجاح');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isStartingTrip = false;
        });
        _showErrorSnackbar('خطأ في بدء الرحلة: ${e.toString()}');
      }
    }
  }

  Future<void> _endTrip() async {
    if (_activeTrip == null) return;

     if (_currentPosition == null) {
      await _getCurrentLocation();
      if (_currentPosition == null) {
         // Error snackbar is shown in _getCurrentLocation
        return;
      }
    }

    setState(() {
      _isEndingTrip = true;
    });

    try {
      await _tripService.endTripWithRealLocation(
        tripId: _activeTrip!.id,
        endLatitude: _currentPosition!.latitude,
        endLongitude: _currentPosition!.longitude,
        startLatitude: _activeTrip!.startLatitude,
        startLongitude: _activeTrip!.startLongitude,
      );

      if (mounted) {
        setState(() {
          _activeTrip = null;
          _isEndingTrip = false;
        });
        _showSuccessSnackbar('تم إنهاء الرحلة بنجاح');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isEndingTrip = false;
        });
        _showErrorSnackbar('خطأ في إنهاء الرحلة: ${e.toString()}');
      }
    }
  }

  void _showSuccessSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.white, size: 20),
             const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppTheme.accentGreen,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackbar(String message) {
     ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppTheme.accentRed,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        margin: const EdgeInsets.all(16),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      body: SafeArea(
        child: Directionality(
          textDirection: ui.TextDirection.rtl,
          child: Column(
             crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16), // Consistent spacing
               // Enhanced Header
               Padding(
                 padding: const EdgeInsets.symmetric(horizontal: 16.0), // Consistent padding
                 child: Row(
                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                   children: [
                     IconButton(
                       onPressed: () => Navigator.pop(context),
                       icon: Container(
                         padding: const EdgeInsets.all(AppTheme.spacingSmall),
                         decoration: AppTheme.getCardDecoration(), // Consistent decoration
                         child: Icon(Icons.arrow_back_ios_new, 
                           color: AppTheme.primaryColor,
                           size: AppTheme.iconSizeSmall,
                         ),
                       ),
                     ),
                     Text(
                       'إدارة الرحلات',
                       style: AppTheme.getTitleStyle(), // Consistent title style
                     ),
                      IconButton(
                        icon: Container(
                          padding: const EdgeInsets.all(AppTheme.spacingSmall),
                          decoration: AppTheme.getCardDecoration(),
                           child: Icon(Icons.refresh, 
                             color: AppTheme.primaryColor,
                              size: AppTheme.iconSizeSmall,
                            ),
                         ),
                         onPressed: _loadData,
                         tooltip: 'تحديث',
                      ),
                   ],
                 ),
               ),
              const SizedBox(height: 24), // Consistent spacing
              Expanded(
                child: _buildContent(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: AppTheme.primaryColor,
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24), // Consistent padding
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: AppTheme.accentRed,
                size: 48, // Consistent icon size
              ),
              const SizedBox(height: 16), // Consistent spacing
              Text(
                'خطأ في تحميل البيانات',
                style: AppTheme.getTitleStyle().copyWith(fontSize: 20), // Use AppTheme style
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8), // Consistent spacing
              Text(
                _errorMessage!,
                style: AppTheme.getBodyStyle(), // Use AppTheme style
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24), // Consistent spacing
              ElevatedButton(
                onPressed: _loadData,
                style: AppTheme.getPrimaryButtonStyle(), // Use AppTheme style
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    if (_activeTrip != null) {
      // Display active trip information and End Trip button
      return SingleChildScrollView(
         padding: const EdgeInsets.all(16), // Consistent padding
        child: Column(
           crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Active Trip Card
            Container(
              padding: const EdgeInsets.all(16), // Consistent padding
               decoration: AppTheme.getCardDecoration(), // Consistent decoration
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'رحلة جارية',
                    style: AppTheme.getTitleStyle().copyWith(fontSize: 20), // Use AppTheme style
                  ),
                   const SizedBox(height: 16), // Consistent spacing
                  _buildTripInfoRow(
                    icon: Icons.route_outlined,
                    label: 'وقت البدء',
                    value: DateFormat('HH:mm', 'ar').format(_activeTrip!.startTime),
                    iconColor: AppTheme.primaryColor,
                  ),
                  const SizedBox(height: 12), // Consistent spacing
                   _buildTripInfoRow(
                    icon: Icons.directions_car_outlined,
                    label: 'المركبة',
                    value: _currentDriver?.vehicles.first.plateNumber ?? 'غير متوفر',
                    iconColor: AppTheme.secondaryColor,
                  ),
                   const SizedBox(height: 12), // Consistent spacing
                   _buildTripInfoRow(
                    icon: Icons.location_on_outlined,
                    label: 'نقطة البدء',
                    value: _activeTrip!.startLocation ?? 'عنوان غير متوفر',
                     iconColor: AppTheme.accentGreen,
                  ),

                ],
              ),
            ),
             const SizedBox(height: 24), // Consistent spacing
            // End Trip Button
             ElevatedButton.icon(
               onPressed: _isEndingTrip ? null : _endTrip,
               style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.accentRed, // Red color for ending trip
                  foregroundColor: Colors.white,
                   elevation: 0,
                   shape: RoundedRectangleBorder(
                     borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                   ),
                    padding: const EdgeInsets.symmetric(vertical: 16), // Fixed padding
               ),
                icon: _isEndingTrip
                   ? const SizedBox(width: 18, height: 18, child: CircularProgressIndicator(strokeWidth: 2, valueColor: AlwaysStoppedAnimation<Color>(Colors.white)))
                    : const Icon(Icons.stop, size: 20),
               label: _isEndingTrip ? const Text('جاري الإنهاء...') : const Text('إنهاء الرحلة'),
             ),
          ],
        ),
      );
    } else if (_currentDriver != null && _currentDriver!.vehicles.isNotEmpty) {
      // Display Start Trip button
      return Padding(
         padding: const EdgeInsets.all(16), // Consistent padding
        child: Column(
           crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            Container(
              padding: const EdgeInsets.all(24), // Consistent padding
              decoration: BoxDecoration(
                color: AppTheme.primaryLightColor,
                 borderRadius: BorderRadius.circular(AppTheme.borderRadiusXLarge), // Consistent border radius
              ),
              child: Icon(
                Icons.assistant_navigation,
                color: AppTheme.primaryColor,
                size: 64, // Consistent icon size
              ),
            ),
            const SizedBox(height: 24), // Consistent spacing
             Text(
               'ابدأ رحلة جديدة',
                style: AppTheme.getTitleStyle().copyWith(fontSize: 20), // Use AppTheme style
                textAlign: TextAlign.center,
             ),
            const SizedBox(height: 8), // Consistent spacing
             Text(
               'انقر على الزر أدناه لبدء تسجيل رحلتك',
                style: AppTheme.getBodyStyle(), // Use AppTheme style
                 textAlign: TextAlign.center,
             ),
            const SizedBox(height: 32), // Larger spacing before button
            ElevatedButton.icon(
              onPressed: _isStartingTrip ? null : _startTrip,
              style: AppTheme.getPrimaryButtonStyle(),
              icon: _isStartingTrip
                   ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2, valueColor: AlwaysStoppedAnimation<Color>(Colors.white)))
                   : const Icon(Icons.play_arrow, size: 24),
              label: _isStartingTrip ? const Text('جاري البدء...') : const Text('بدء رحلة جديدة'),
            ),
          ],
        ),
      );
    } else {
      // Display message to add a vehicle
       return Padding(
          padding: const EdgeInsets.all(16), // Consistent padding
         child: Column(
           crossAxisAlignment: CrossAxisAlignment.stretch,
           mainAxisAlignment: MainAxisAlignment.center,
           children: [
             // Icon
             Container(
               padding: const EdgeInsets.all(24), // Consistent padding
               decoration: BoxDecoration(
                 color: AppTheme.accentRedLight,
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusXLarge), // Consistent border radius
               ),
               child: const Icon(
                 Icons.car_crash_outlined,
                 color: AppTheme.accentRed,
                 size: 64, // Consistent icon size
               ),
             ),
              const SizedBox(height: 24), // Consistent spacing
              Text(
                'لا توجد مركبات متاحة',
                 style: AppTheme.getTitleStyle().copyWith(fontSize: 20), // Use AppTheme style
                 textAlign: TextAlign.center,
              ),
             const SizedBox(height: 8), // Consistent spacing
              Text(
                'يرجى إضافة مركبة جديدة للتمكن من إدارة الرحلات.',
                 style: AppTheme.getBodyStyle(), // Use AppTheme style
                  textAlign: TextAlign.center,
              ),
             const SizedBox(height: 24), // Consistent spacing
             ElevatedButton.icon(
               onPressed: () {
                 // TODO: Navigate to Add Vehicle Screen/BottomSheet
               },
                style: AppTheme.getPrimaryButtonStyle(),
               icon: const Icon(Icons.add, size: 24),
               label: const Text('إضافة مركبة جديدة'),
             ),
           ],
         ),
       );
    }
  }

  Widget _buildTripInfoRow({
    required IconData icon,
    required String label,
    required String value,
    required Color iconColor,
  }) {
    return Row(
      children: [
        Container(
           padding: const EdgeInsets.all(8), // Consistent padding
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8), // Rounded corners
            ),
           child: Icon(icon, color: iconColor, size: 20), // Consistent icon size
         ),
         const SizedBox(width: 16), // Consistent spacing
         Expanded(
           child: Text(
             label,
             style: AppTheme.getBodyStyle(), // Consistent text style
           ),
         ),
         Text(
           value,
           style: AppTheme.getSubtitleStyle(), // Consistent text style
         ),
      ],
    );
  }
}
