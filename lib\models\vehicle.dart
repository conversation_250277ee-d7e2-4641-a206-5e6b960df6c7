enum VehicleStatus { ACTIVE, INACTIVE, MAINTENANCE }

enum FuelType { GASOLINE, DIESEL, ELECTRIC, HYBRID }

class Vehicle {
  final String id;
  final String model;
  final int year;
  final String plateNumber;
  final VehicleStatus status;
  final FuelType fuelType;
  final double fuelLevel;
  final DateTime insuranceExpiry;
  final DateTime createdAt;
  final DateTime updatedAt;

  Vehicle({
    required this.id,
    required this.model,
    required this.year,
    required this.plateNumber,
    required this.status,
    required this.fuelType,
    required this.fuelLevel,
    required this.insuranceExpiry,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Vehicle.fromJson(Map<String, dynamic> json) {
    return Vehicle(
      id: json['id'] as String,
      model: json['model'] as String,
      year: json['year'] as int,
      plateNumber: json['plateNumber'] as String,
      status: VehicleStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => VehicleStatus.ACTIVE,
      ),
      fuelType: FuelType.values.firstWhere(
        (e) => e.name == json['fuelType'],
        orElse: () => FuelType.GASOLINE,
      ),
      fuelLevel: (json['fuelLevel'] as num).toDouble(),
      insuranceExpiry: DateTime.parse(json['insuranceExpiry'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'model': model,
      'year': year,
      'plateNumber': plateNumber,
      'status': status.name,
      'fuelType': fuelType.name,
      'fuelLevel': fuelLevel,
      'insuranceExpiry': insuranceExpiry.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  String get fuelTypeDisplayName {
    switch (fuelType) {
      case FuelType.GASOLINE:
        return 'بنزين';
      case FuelType.DIESEL:
        return 'ديزل';
      case FuelType.ELECTRIC:
        return 'كهربائي';
      case FuelType.HYBRID:
        return 'هجين';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case VehicleStatus.ACTIVE:
        return 'نشط';
      case VehicleStatus.INACTIVE:
        return 'غير نشط';
      case VehicleStatus.MAINTENANCE:
        return 'صيانة';
    }
  }

  @override
  String toString() {
    return 'Vehicle{id: $id, model: $model, plateNumber: $plateNumber, status: $status}';
  }
}
