import 'package:flutter/material.dart';
import 'dart:math' as math;

class SemiCirclePainter extends CustomPainter {
  final double value; // Value between 0.0 and 1.0
  final Color backgroundColor;
  final Color progressColor;
  final double strokeWidth;

  SemiCirclePainter({
    required this.value,
    this.backgroundColor = Colors.white24,
    this.progressColor = Colors.white,
    this.strokeWidth = 10.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height);
    final radius = size.width / 2;
    
    // Draw background arc
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;
    
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi, // Start from the left (180 degrees)
      math.pi, // Draw a semi-circle (180 degrees)
      false,
      backgroundPaint,
    );
    
    // Draw progress arc
    final progressPaint = Paint()
      ..color = progressColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;
    
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi, // Start from the left (180 degrees)
      math.pi * value, // Draw progress based on value
      false,
      progressPaint,
    );
    
    // Draw tick marks
    final tickPaint = Paint()
      ..color = Colors.white30
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    for (int i = 0; i <= 10; i++) {
      final angle = math.pi + (math.pi / 10) * i;
      final outerPoint = Offset(
        center.dx + (radius + 5) * math.cos(angle),
        center.dy + (radius + 5) * math.sin(angle),
      );
      final innerPoint = Offset(
        center.dx + (radius - 5) * math.cos(angle),
        center.dy + (radius - 5) * math.sin(angle),
      );
      
      canvas.drawLine(innerPoint, outerPoint, tickPaint);
    }
  }

  @override
  bool shouldRepaint(SemiCirclePainter oldDelegate) {
    return oldDelegate.value != value ||
        oldDelegate.backgroundColor != backgroundColor ||
        oldDelegate.progressColor != progressColor ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}
