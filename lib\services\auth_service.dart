import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'api_service.dart';
import 'api_error.dart';
import 'storage_service.dart';

class AuthService {
  final ApiService _apiService = ApiService();
  final StorageService _storageService = StorageService();

  Timer? _refreshTimer;

  // Login method - supports both email and username
  Future<Map<String, dynamic>> login(
      String emailOrUsername, String password) async {
    if (kDebugMode) {
      print('🔐 AUTH: Starting login for: $emailOrUsername');
    }

    try {
      // Determine if input is email or username
      final bool isEmail = emailOrUsername.contains('@');

      final Map<String, dynamic> loginData = {
        'password': password,
      };

      if (isEmail) {
        loginData['email'] = emailOrUsername;
      } else {
        loginData['username'] = emailOrUsername;
      }

      final response = await _apiService.postAuth('api/auth/login', loginData);

      if (kDebugMode) {
        print('🔐 AUTH: Login response received: $response');
      }

      if (response['success'] == true && response['data'] != null) {
        final data = response['data'];
        final accessToken = data['accessToken'];
        final refreshToken = data['refreshToken'];

        if (kDebugMode) {
          print('🔐 AUTH: Login successful, storing tokens');
        }

        // Store tokens securely
        await _storeTokens(accessToken, refreshToken);

        // Set access token in API service
        _apiService.setAccessToken(accessToken);

        // Start refresh timer
        _startRefreshTimer();

        if (kDebugMode) {
          print('🔐 AUTH: Login completed successfully');
        }

        return response;
      } else {
        if (kDebugMode) {
          print('🔐 AUTH: Login failed - invalid response format');
        }
        throw ApiException(ApiError(
          message: 'Login Failed',
          details: 'Invalid response format',
        ));
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔐 AUTH: Login error: $e');
      }
      rethrow;
    }
  }

  // Store tokens securely
  Future<void> _storeTokens(String accessToken, String refreshToken) async {
    if (kDebugMode) {
      print('🔐 AUTH: Storing tokens securely');
    }

    // Calculate expiry time (24 hours from now minus 1 hour buffer)
    final expiryTime = DateTime.now().add(const Duration(hours: 23));

    try {
      await Future.wait([
        _storageService.storeAccessToken(accessToken),
        _storageService.storeRefreshToken(refreshToken),
        _storageService.storeTokenExpiry(expiryTime.toIso8601String()),
      ]);

      if (kDebugMode) {
        print('🔐 AUTH: Tokens stored successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔐 AUTH: Error storing tokens: $e');
      }
      rethrow;
    }
  }

  // Get stored access token
  Future<String?> getAccessToken() async {
    return await _storageService.getAccessToken();
  }

  // Get stored refresh token
  Future<String?> getRefreshToken() async {
    return await _storageService.getRefreshToken();
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final accessToken = await getAccessToken();
    final refreshToken = await getRefreshToken();
    return accessToken != null && refreshToken != null;
  }

  // Initialize auth service (call this on app start)
  Future<void> initialize() async {
    final accessToken = await getAccessToken();
    if (accessToken != null) {
      _apiService.setAccessToken(accessToken);

      // Check if token needs refresh
      if (await _shouldRefreshToken()) {
        await _refreshAccessToken();
      }

      // Start refresh timer
      _startRefreshTimer();
    }
  }

  // Check if token should be refreshed
  Future<bool> _shouldRefreshToken() async {
    final expiryString = await _storageService.getTokenExpiry();
    if (expiryString == null) return true;

    final expiryTime = DateTime.parse(expiryString);
    final now = DateTime.now();

    // Refresh if token expires within 1 hour
    return now.isAfter(expiryTime.subtract(const Duration(hours: 1)));
  }

  // Refresh access token
  Future<void> _refreshAccessToken() async {
    if (kDebugMode) {
      print('🔄 AUTH: Starting token refresh');
    }

    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null) {
        if (kDebugMode) {
          print('🔄 AUTH: No refresh token found, logging out');
        }
        await logout();
        return;
      }

      if (kDebugMode) {
        print('🔄 AUTH: Calling refresh token endpoint');
      }

      final response = await _apiService.postAuth('api/auth/refresh-token', {
        'refreshToken': refreshToken,
      });

      if (kDebugMode) {
        print('🔄 AUTH: Refresh response received: $response');
      }

      if (response['success'] == true && response['data'] != null) {
        final data = response['data'];
        final newAccessToken = data['accessToken'];
        final newRefreshToken = data['refreshToken'];

        if (kDebugMode) {
          print('🔄 AUTH: Token refresh successful, storing new tokens');
        }

        // Store new tokens
        await _storeTokens(newAccessToken, newRefreshToken);

        // Update API service with new token
        _apiService.setAccessToken(newAccessToken);

        if (kDebugMode) {
          print('🔄 AUTH: Token refresh completed successfully');
        }
      } else {
        if (kDebugMode) {
          print(
              '🔄 AUTH: Token refresh failed - invalid response, logging out');
        }
        // If refresh fails, logout user
        await logout();
      }
    } catch (e) {
      if (kDebugMode) {
        print('🔄 AUTH: Token refresh error: $e, logging out');
      }
      // If refresh fails, logout user
      await logout();
    }
  }

  // Start automatic token refresh timer
  void _startRefreshTimer() {
    _refreshTimer?.cancel();

    // Check every hour if token needs refresh
    _refreshTimer = Timer.periodic(const Duration(hours: 1), (timer) async {
      if (await _shouldRefreshToken()) {
        await _refreshAccessToken();
      }
    });
  }

  // Logout method
  Future<void> logout() async {
    // Clear tokens from secure storage
    await _storageService.clearAllTokens();

    // Clear token from API service
    _apiService.clearAccessToken();

    // Cancel refresh timer
    _refreshTimer?.cancel();
  }

  // Get current user info (if available in token)
  Future<Map<String, dynamic>?> getCurrentUser() async {
    final accessToken = await getAccessToken();
    if (accessToken == null) return null;

    try {
      // Decode JWT token to get user info (basic implementation)
      final parts = accessToken.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      final normalized = base64Url.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      final payloadMap = json.decode(decoded);

      return payloadMap;
    } catch (e) {
      return null;
    }
  }

  // Dispose method
  void dispose() {
    _refreshTimer?.cancel();
  }
}
