class ApiError {
  final int? statusCode;
  final String message;
  final String? details;
  final Map<String, dynamic>? data;

  ApiError({
    this.statusCode,
    required this.message,
    this.details,
    this.data,
  });

  factory ApiError.fromResponse(int statusCode, Map<String, dynamic>? responseData) {
    String message;
    String? details;

    switch (statusCode) {
      case 400:
        message = 'Bad Request';
        details = responseData?['message'] ?? 'Invalid request parameters';
        break;
      case 401:
        message = 'Unauthorized';
        details = responseData?['message'] ?? 'Authentication required';
        break;
      case 403:
        message = 'Forbidden';
        details = responseData?['message'] ?? 'Access denied';
        break;
      case 404:
        message = 'Not Found';
        details = responseData?['message'] ?? 'Resource not found';
        break;
      case 422:
        message = 'Validation Error';
        details = responseData?['message'] ?? 'Validation failed';
        break;
      case 429:
        message = 'Too Many Requests';
        details = responseData?['message'] ?? 'Rate limit exceeded';
        break;
      case 500:
        message = 'Internal Server Error';
        details = responseData?['message'] ?? 'Server error occurred';
        break;
      case 502:
        message = 'Bad Gateway';
        details = responseData?['message'] ?? 'Server is temporarily unavailable';
        break;
      case 503:
        message = 'Service Unavailable';
        details = responseData?['message'] ?? 'Service is temporarily unavailable';
        break;
      default:
        message = 'Unknown Error';
        details = responseData?['message'] ?? 'An unexpected error occurred';
    }

    return ApiError(
      statusCode: statusCode,
      message: message,
      details: details,
      data: responseData,
    );
  }

  factory ApiError.networkError() {
    return ApiError(
      message: 'Network Error',
      details: 'Please check your internet connection',
    );
  }

  factory ApiError.timeoutError() {
    return ApiError(
      message: 'Request Timeout',
      details: 'The request took too long to complete',
    );
  }

  factory ApiError.unknownError() {
    return ApiError(
      message: 'Unknown Error',
      details: 'An unexpected error occurred',
    );
  }

  @override
  String toString() {
    return 'ApiError(statusCode: $statusCode, message: $message, details: $details)';
  }
}

class ApiException implements Exception {
  final ApiError error;

  ApiException(this.error);

  @override
  String toString() {
    return error.toString();
  }
}
