import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';
import '../models/trip.dart';
import '../models/driver.dart';

class ReportsStorageService {
  static Database? _database;
  static const String _dbName = 'drivety_reports.db';
  static const int _dbVersion = 1;

  // Table names
  static const String _tripsTable = 'trips_cache';
  static const String _driverTable = 'driver_cache';
  static const String _reportsTable = 'reports_data';

  // Get database instance
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // Initialize database
  Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, _dbName);

    if (kDebugMode) {
      print('📊 REPORTS DB: Initializing database at: $path');
    }

    return await openDatabase(
      path,
      version: _dbVersion,
      onCreate: _createTables,
      onUpgrade: _onUpgrade,
    );
  }

  // Create database tables
  Future<void> _createTables(Database db, int version) async {
    if (kDebugMode) {
      print('📊 REPORTS DB: Creating tables...');
    }

    // Trips cache table
    await db.execute('''
      CREATE TABLE $_tripsTable (
        id TEXT PRIMARY KEY,
        driver_id TEXT NOT NULL,
        vehicle_id TEXT NOT NULL,
        start_location TEXT NOT NULL,
        end_location TEXT,
        start_latitude REAL,
        start_longitude REAL,
        end_latitude REAL,
        end_longitude REAL,
        start_time TEXT NOT NULL,
        end_time TEXT,
        distance REAL,
        fuel_consumed REAL,
        status TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Driver cache table
    await db.execute('''
      CREATE TABLE $_driverTable (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        phone TEXT,
        driver_score REAL NOT NULL,
        score_grade TEXT NOT NULL,
        total_trips INTEGER NOT NULL,
        total_distance REAL NOT NULL,
        cached_at TEXT NOT NULL
      )
    ''');

    // Reports data table for aggregated statistics
    await db.execute('''
      CREATE TABLE $_reportsTable (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        driver_id TEXT NOT NULL,
        period_type TEXT NOT NULL,
        period_start TEXT NOT NULL,
        period_end TEXT NOT NULL,
        total_trips INTEGER NOT NULL,
        total_distance REAL NOT NULL,
        total_fuel REAL NOT NULL,
        total_duration INTEGER NOT NULL,
        avg_fuel_efficiency REAL NOT NULL,
        avg_speed REAL NOT NULL,
        safety_score REAL NOT NULL,
        created_at TEXT NOT NULL
      )
    ''');

    if (kDebugMode) {
      print('📊 REPORTS DB: Tables created successfully');
    }
  }

  // Handle database upgrades
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (kDebugMode) {
      print('📊 REPORTS DB: Upgrading from version $oldVersion to $newVersion');
    }
    // Handle future database schema changes here
  }

  // Cache trip data
  Future<void> cacheTrip(Trip trip) async {
    try {
      final db = await database;

      await db.insert(
        _tripsTable,
        {
          'id': trip.id,
          'driver_id': trip.driverId,
          'vehicle_id': trip.vehicleId,
          'start_location': trip.startLocation,
          'end_location': trip.endLocation,
          'start_latitude': trip.startLatitude,
          'start_longitude': trip.startLongitude,
          'end_latitude': trip.endLatitude,
          'end_longitude': trip.endLongitude,
          'start_time': trip.startTime.toIso8601String(),
          'end_time': trip.endTime?.toIso8601String(),
          'distance': trip.distance,
          'fuel_consumed': trip.fuelConsumed,
          'status': trip.status.name,
          'created_at': trip.startTime.toIso8601String(),
          'updated_at': trip.updatedAt.toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      if (kDebugMode) {
        print('📊 REPORTS DB: Cached trip ${trip.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS DB: Error caching trip: $e');
      }
    }
  }

  // Cache multiple trips
  Future<void> cacheTrips(List<Trip> trips) async {
    final db = await database;
    final batch = db.batch();

    for (final trip in trips) {
      if (kDebugMode) {
        print(
            '📊 REPORTS DB: Caching trip ${trip.id} - Distance: ${trip.distance}km, Fuel: ${trip.fuelConsumed}L, Status: ${trip.status.name}');
      }

      batch.insert(
        _tripsTable,
        {
          'id': trip.id,
          'driver_id': trip.driverId,
          'vehicle_id': trip.vehicleId,
          'start_location': trip.startLocation,
          'end_location': trip.endLocation,
          'start_latitude': trip.startLatitude,
          'start_longitude': trip.startLongitude,
          'end_latitude': trip.endLatitude,
          'end_longitude': trip.endLongitude,
          'start_time': trip.startTime.toIso8601String(),
          'end_time': trip.endTime?.toIso8601String(),
          'distance': trip.distance,
          'fuel_consumed': trip.fuelConsumed,
          'status': trip.status.name,
          'created_at': trip.startTime.toIso8601String(),
          'updated_at': trip.updatedAt.toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    await batch.commit();

    if (kDebugMode) {
      print('📊 REPORTS DB: Cached ${trips.length} trips');
    }
  }

  // Cache driver data
  Future<void> cacheDriver(Driver driver) async {
    try {
      final db = await database;

      await db.insert(
        _driverTable,
        {
          'id': driver.id,
          'name': driver.name,
          'email': '', // Not available in current Driver model
          'phone': driver.phoneNumber,
          'driver_score': driver.driverScore,
          'score_grade': driver.scoreGrade,
          'total_trips': 0, // Will be calculated from trips
          'total_distance': 0.0, // Will be calculated from trips
          'cached_at': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      if (kDebugMode) {
        print('📊 REPORTS DB: Cached driver ${driver.id}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS DB: Error caching driver: $e');
      }
    }
  }

  // Get cached trips for a driver
  Future<List<Trip>> getCachedTrips(String driverId) async {
    try {
      final db = await database;
      final maps = await db.query(
        _tripsTable,
        where: 'driver_id = ?',
        whereArgs: [driverId],
        orderBy: 'start_time DESC',
      );

      return maps
          .map((map) => Trip.fromJson({
                'id': map['id'],
                'driverId': map['driver_id'],
                'vehicleId': map['vehicle_id'],
                'startLocation': map['start_location'],
                'endLocation': map['end_location'],
                'startLatitude': map['start_latitude'],
                'startLongitude': map['start_longitude'],
                'endLatitude': map['end_latitude'],
                'endLongitude': map['end_longitude'],
                'startTime': map['start_time'],
                'endTime': map['end_time'],
                'distance': map['distance'],
                'fuelConsumed': map['fuel_consumed'],
                'status': map['status'],
                'updatedAt': map['updated_at'],
              }))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS DB: Error getting cached trips: $e');
      }
      return [];
    }
  }

  // Get cached driver data
  Future<Driver?> getCachedDriver(String driverId) async {
    try {
      final db = await database;
      final maps = await db.query(
        _driverTable,
        where: 'id = ?',
        whereArgs: [driverId],
        limit: 1,
      );

      if (maps.isEmpty) return null;

      final map = maps.first;
      return Driver.fromJson({
        'id': map['id'],
        'userId': '', // Not stored in cache
        'name': map['name'],
        'licenseNumber': '', // Not stored in cache
        'licenseExpiry':
            DateTime.now().add(const Duration(days: 365)).toIso8601String(),
        'phoneNumber': map['phone'],
        'driverScore': map['driver_score'],
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
        'vehicles': [], // Will be loaded separately if needed
      });
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS DB: Error getting cached driver: $e');
      }
      return null;
    }
  }

  // Clear old cache data (older than 30 days)
  Future<void> clearOldCache() async {
    try {
      final db = await database;
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));

      await db.delete(
        _tripsTable,
        where: 'created_at < ?',
        whereArgs: [thirtyDaysAgo.toIso8601String()],
      );

      if (kDebugMode) {
        print('📊 REPORTS DB: Cleared old cache data');
      }
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS DB: Error clearing old cache: $e');
      }
    }
  }

  // Clear ALL cache data (force complete refresh)
  Future<void> clearAllCache() async {
    try {
      final db = await database;

      await db.delete(_tripsTable);
      await db.delete(_driverTable);
      await db.delete(_reportsTable);

      if (kDebugMode) {
        print('📊 REPORTS DB: Cleared ALL cache data (complete refresh)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS DB: Error clearing all cache: $e');
      }
    }
  }

  // Store aggregated reports data
  Future<void> storeReportsData(
      String driverId,
      String periodType,
      DateTime periodStart,
      DateTime periodEnd,
      Map<String, dynamic> data) async {
    try {
      final db = await database;

      await db.insert(
        _reportsTable,
        {
          'driver_id': driverId,
          'period_type': periodType,
          'period_start': periodStart.toIso8601String(),
          'period_end': periodEnd.toIso8601String(),
          'total_trips': data['totalTrips'] ?? 0,
          'total_distance': data['totalDistance'] ?? 0.0,
          'total_fuel': data['totalFuel'] ?? 0.0,
          'total_duration': data['totalDuration'] ?? 0,
          'avg_fuel_efficiency': data['avgFuelEfficiency'] ?? 0.0,
          'avg_speed': data['avgSpeed'] ?? 0.0,
          'safety_score': data['safetyScore'] ?? 0.0,
          'created_at': DateTime.now().toIso8601String(),
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      if (kDebugMode) {
        print('📊 REPORTS DB: Stored reports data for $periodType period');
      }
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS DB: Error storing reports data: $e');
      }
    }
  }

  // Get stored reports data
  Future<Map<String, dynamic>?> getStoredReportsData(String driverId,
      String periodType, DateTime periodStart, DateTime periodEnd) async {
    try {
      final db = await database;
      final maps = await db.query(
        _reportsTable,
        where:
            'driver_id = ? AND period_type = ? AND period_start = ? AND period_end = ?',
        whereArgs: [
          driverId,
          periodType,
          periodStart.toIso8601String(),
          periodEnd.toIso8601String()
        ],
        orderBy: 'created_at DESC',
        limit: 1,
      );

      if (maps.isEmpty) return null;

      final map = maps.first;
      return {
        'totalTrips': map['total_trips'],
        'totalDistance': map['total_distance'],
        'totalFuel': map['total_fuel'],
        'totalDuration': map['total_duration'],
        'avgFuelEfficiency': map['avg_fuel_efficiency'],
        'avgSpeed': map['avg_speed'],
        'safetyScore': map['safety_score'],
        'createdAt': map['created_at'],
      };
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS DB: Error getting stored reports data: $e');
      }
      return null;
    }
  }

  // Get trips for a specific period
  Future<List<Trip>> getTripsForPeriod(
      String driverId, DateTime start, DateTime end) async {
    try {
      final db = await database;
      final maps = await db.query(
        _tripsTable,
        where: 'driver_id = ? AND start_time >= ? AND start_time <= ?',
        whereArgs: [driverId, start.toIso8601String(), end.toIso8601String()],
        orderBy: 'start_time ASC',
      );

      return maps
          .map((map) => Trip.fromJson({
                'id': map['id'],
                'driverId': map['driver_id'],
                'vehicleId': map['vehicle_id'],
                'startLocation': map['start_location'],
                'endLocation': map['end_location'],
                'startLatitude': map['start_latitude'],
                'startLongitude': map['start_longitude'],
                'endLatitude': map['end_latitude'],
                'endLongitude': map['end_longitude'],
                'startTime': map['start_time'],
                'endTime': map['end_time'],
                'distance': map['distance'],
                'fuelConsumed': map['fuel_consumed'],
                'status': map['status'],
                'updatedAt': map['updated_at'],
              }))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS DB: Error getting trips for period: $e');
      }
      return [];
    }
  }

  // Update trip data when trip is completed
  Future<void> updateTripData(
    String tripId, {
    double? distance,
    double? fuelConsumed,
    String? endLocation,
    double? endLatitude,
    double? endLongitude,
    DateTime? endTime,
    String? status,
  }) async {
    try {
      final db = await database;

      final updateData = <String, dynamic>{};

      if (distance != null) updateData['distance'] = distance;
      if (fuelConsumed != null) updateData['fuel_consumed'] = fuelConsumed;
      if (endLocation != null) updateData['end_location'] = endLocation;
      if (endLatitude != null) updateData['end_latitude'] = endLatitude;
      if (endLongitude != null) updateData['end_longitude'] = endLongitude;
      if (endTime != null) updateData['end_time'] = endTime.toIso8601String();
      if (status != null) updateData['status'] = status;

      updateData['updated_at'] = DateTime.now().toIso8601String();

      final rowsAffected = await db.update(
        _tripsTable,
        updateData,
        where: 'id = ?',
        whereArgs: [tripId],
      );

      if (kDebugMode) {
        print(
            '📊 REPORTS DB: Updated trip $tripId - Distance: ${distance}km, Fuel: ${fuelConsumed}L (Rows affected: $rowsAffected)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS DB: Error updating trip data: $e');
      }
    }
  }

  // Force refresh trip data from API
  Future<void> refreshTripData(String tripId, Trip updatedTrip) async {
    try {
      final db = await database;

      await db.update(
        _tripsTable,
        {
          'start_location': updatedTrip.startLocation,
          'end_location': updatedTrip.endLocation,
          'start_latitude': updatedTrip.startLatitude,
          'start_longitude': updatedTrip.startLongitude,
          'end_latitude': updatedTrip.endLatitude,
          'end_longitude': updatedTrip.endLongitude,
          'start_time': updatedTrip.startTime.toIso8601String(),
          'end_time': updatedTrip.endTime?.toIso8601String(),
          'distance': updatedTrip.distance,
          'fuel_consumed': updatedTrip.fuelConsumed,
          'status': updatedTrip.status.name,
          'updated_at': updatedTrip.updatedAt.toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [tripId],
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      if (kDebugMode) {
        print(
            '📊 REPORTS DB: Refreshed trip $tripId with updated data - Distance: ${updatedTrip.distance}km, Fuel: ${updatedTrip.fuelConsumed}L');
      }
    } catch (e) {
      if (kDebugMode) {
        print('📊 REPORTS DB: Error refreshing trip data: $e');
      }
    }
  }

  // Close database
  Future<void> close() async {
    if (_database != null) {
      await _database!.close();
      _database = null;
    }
  }
}
