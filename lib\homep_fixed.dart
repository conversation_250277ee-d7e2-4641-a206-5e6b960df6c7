import 'package:flutter/material.dart';
import 'dart:ui';
import 'utils/responsive_size.dart';
import 'utils/app_theme.dart';
import 'widgets/open_street_map.dart';
import 'widgets/obd_connection_dialog.dart';
import 'map_screen_fixed.dart';
import 'trip_details_screen.dart';
import 'services/obd_service.dart';
import 'package:flutter/services.dart';

// استيراد صفحات التنقل
import 'trips_screen.dart';
import 'reports_screen.dart';
import 'settings_page.dart';
import 'notifications_screen.dart';

// Import models and services
import 'models/driver.dart';
import 'models/trip.dart';
import 'models/vehicle.dart';
import 'services/driver_service.dart';
import 'services/trip_service.dart';
import 'services/auth_service.dart';
import 'vehicle_screen.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with SingleTickerProviderStateMixin {
  final ResponsiveSize _responsiveSize = ResponsiveSize();
  int _currentIndex = 0; // مؤشر الصفحة الحالية
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  // Calculate bottom navigation bar height including margins
  double get _navigationBarHeight => ResponsiveSize.height(8) + ResponsiveSize.height(4); // bar height + margins

  // قائمة بصفحات التطبيق
  List<Widget> get _pages => [
    _buildPageWithBottomPadding(const HomeContent()), // محتوى الصفحة الرئيسية
    _buildPageWithBottomPadding(const TripsScreen()),
    _buildPageWithBottomPadding(const ReportsScreen()),
    _buildPageWithBottomPadding(const SettingsPage()),
  ];

  // Wrapper to add bottom padding for navigation bar
  Widget _buildPageWithBottomPadding(Widget child) {
    return Padding(
      padding: EdgeInsets.only(bottom: _navigationBarHeight),
      child: child,
    );
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
    );
    _animationController.forward();
    
    // Show first time alert after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      _showFirstTimeAlert();
    });
  }

  void _showFirstTimeAlert() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
            elevation: 0,
            backgroundColor: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with gradient background
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.primaryColor,
                          AppTheme.primaryColor.withOpacity(0.8),
                        ],
                        begin: Alignment.topRight,
                        end: Alignment.bottomLeft,
                      ),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: const Icon(
                            Icons.car_repair,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'مرحباً بك في درايفتي',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                'ابدأ رحلتك معنا',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  // Content
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'للاستفادة الكاملة من التطبيق، يرجى:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textDark,
                          ),
                        ),
                        const SizedBox(height: 20),
                        _buildModernAlertItem(
                          icon: Icons.directions_car,
                          text: 'إضافة مركبتك الأولى',
                          description: 'قم بإضافة تفاصيل مركبتك للبدء',
                        ),
                        const SizedBox(height: 16),
                        GestureDetector(
                          onTap: _showOBDConnectionDialog,
                          child: _buildModernAlertItem(
                            icon: Icons.bluetooth,
                            text: 'ربط جهاز OBD II',
                            description: 'اتصل بجهاز OBD II للحصول على بيانات المركبة',
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildModernAlertItem(
                          icon: Icons.info_outline,
                          text: 'توجيه خطوة بخطوة',
                          description: 'سنقوم بتوجيهك خلال عملية الإعداد',
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  // Buttons
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(
                                color: AppTheme.primaryColor.withOpacity(0.5),
                              ),
                            ),
                          ),
                          child: const Text(
                            'لاحقاً',
                            style: TextStyle(
                              color: AppTheme.primaryColor,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const VehicleScreen(),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'إضافة مركبة',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildModernAlertItem({
    required IconData icon,
    required String text,
    required String description,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  text,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textDark,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.textMedium,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showOBDConnectionDialog() async {
    try {
      final result = await showOBDConnectionDialog(context);
      if (result == true && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم الاتصال بجهاز OBD بنجاح'),
            backgroundColor: AppTheme.accentGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل الاتصال بجهاز OBD: ${e.toString()}'),
            backgroundColor: AppTheme.accentRed,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Widget _buildFloatingNavigationBar() {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: ResponsiveSize.width(4),
        vertical: ResponsiveSize.height(2),
      ),
      height: ResponsiveSize.height(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ResponsiveSize.width(6)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildNavItem(
            icon: _currentIndex == 0 ? Icons.home : Icons.home_outlined,
            label: 'الرئيسية',
            index: 0,
            isActive: _currentIndex == 0,
          ),
          _buildNavItem(
            icon: _currentIndex == 1 ? Icons.map : Icons.map_outlined,
            label: 'الرحلات',
            index: 1,
            isActive: _currentIndex == 1,
          ),
          _buildNavItem(
            icon: _currentIndex == 2 ? Icons.assessment : Icons.assessment_outlined,
            label: 'التقارير',
            index: 2,
            isActive: _currentIndex == 2,
          ),
          _buildNavItem(
            icon: _currentIndex == 3 ? Icons.settings : Icons.settings_outlined,
            label: 'الإعدادات',
            index: 3,
            isActive: _currentIndex == 3,
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required int index,
    required bool isActive,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() => _currentIndex = index);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(
          horizontal: ResponsiveSize.width(3),
          vertical: ResponsiveSize.height(1),
        ),
        decoration: BoxDecoration(
          color: isActive
              ? AppTheme.primaryColor.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(ResponsiveSize.width(4)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isActive ? AppTheme.primaryColor : AppTheme.textMedium,
              size: ResponsiveSize.width(6),
            ),
            SizedBox(height: ResponsiveSize.height(0.5)),
            Text(
              label,
              style: TextStyle(
                color: isActive ? AppTheme.primaryColor : AppTheme.textMedium,
                fontSize: ResponsiveSize.width(2.5),
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      extendBody: true,
      body: SafeArea(
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: _pages[_currentIndex],
          ),
        ),
      ),
      floatingActionButton: _buildFloatingNavigationBar(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }
}

// دالة لعرض محتوى الصفحة الرئيسية
class HomeContent extends StatefulWidget {
  const HomeContent({super.key});

  @override
  State<HomeContent> createState() => _HomeContentState();
}

class _HomeContentState extends State<HomeContent> with SingleTickerProviderStateMixin {
  final ResponsiveSize _responsiveSize = ResponsiveSize();
  late AnimationController _contentAnimationController;
  late Animation<double> _contentFadeAnimation;
  late Animation<Offset> _contentSlideAnimation;
  late Animation<double> _contentScaleAnimation;

  // Import the required services and models
  final DriverService _driverService = DriverService();
  final TripService _tripService = TripService();
  final AuthService _authService = AuthService();

  Driver? _currentDriver;
  List<Trip> _recentTrips = [];
  Trip? _activeTrip;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _contentAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _contentFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _contentAnimationController,
        curve: Curves.easeIn,
      ),
    );
    _contentSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _contentAnimationController,
        curve: Curves.easeOut,
      ),
    );
    _contentScaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(
        parent: _contentAnimationController,
        curve: Curves.easeOut,
      ),
    );
    _contentAnimationController.forward();
    _loadData();
  }

  @override
  void dispose() {
    _contentAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final accessToken = await _authService.getAccessToken();
      if (accessToken == null) {
        throw Exception('No access token found');
      }

      // Set access tokens for all services
      _driverService.setAccessToken(accessToken);
      _tripService.setAccessToken(accessToken);

      // Load driver data and recent trips
      _currentDriver = await _driverService.getCurrentUserDriver();
      _recentTrips = await _tripService.getDriverTrips(_currentDriver!.id);

      // Get active trip if any
      _activeTrip = _recentTrips
          .where((trip) => trip.status == TripStatus.IN_PROGRESS)
          .firstOrNull;

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
      print('Error loading home data: $e');
    }
  }



  @override
  Widget build(BuildContext context) {
    // Initialize ResponsiveSize for this context
    ResponsiveSize().init(context);
    _responsiveSize.init(context);
    final isPortrait =
        MediaQuery.of(context).orientation == Orientation.portrait;

    // Define custom gradients
    const primaryGradient = LinearGradient(
      colors: [
        const Color(0xFFFFA500), // Orange
        const Color(0xFFFFB84D), // Lighter Orange
        const Color(0xFFFFD699), // Very Light Orange
      ],
      begin: Alignment.topRight,
      end: Alignment.bottomLeft,
    );

    const backgroundGradient = LinearGradient(
      colors: [
        Colors.white,
        const Color(0xFFFFF5E6), // Very Light Orange Background
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    );

    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: primaryGradient.colors.first,
              strokeWidth: 3,
            ),
            SizedBox(height: ResponsiveSize.height(2)),
            Text(
              'جاري التحميل...',
              style: TextStyle(
                fontSize: ResponsiveSize.fontSize(4),
                color: AppTheme.textMedium,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: primaryGradient.colors.first,
              ),
              const SizedBox(height: 16),
              const Text(
                'خطأ في تحميل البيانات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage!,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textMedium,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadData,
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryGradient.colors.first,
                  foregroundColor: Colors.white,
                ),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    return Directionality(
      textDirection: TextDirection.rtl,
      child: RefreshIndicator(
        onRefresh: _loadData,
        color: primaryGradient.colors.first,
        child: SlideTransition(
          position: _contentSlideAnimation,
          child: FadeTransition(
            opacity: _contentFadeAnimation,
            child: ScaleTransition(
              scale: _contentScaleAnimation,
              child: CustomScrollView(
                slivers: [
                  // Enhanced App Bar with Orange Gradient
                  SliverAppBar(
                    floating: true,
                    snap: true,
                    pinned: true,
                    elevation: 0,
                    backgroundColor: Colors.transparent,
                    expandedHeight: 70,
                    flexibleSpace: Container(
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.white, // White on the logo side
                            Color(0xFFFFF5E6), // Very Light Orange
                          ],
                          begin: Alignment.centerRight, // Start from the right (logo side)
                          end: Alignment.centerLeft,
                        ),
                      ),
                    ),
                    title: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            _buildHeaderButton(
                              icon: Icons.settings,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const SettingsPage(),
                                  ),
                                );
                              },
                              isLight: true,
                            ),
                            const SizedBox(width: 12),
                            _buildHeaderButton(
                              icon: Icons.notifications_outlined,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const NotificationsScreen(),
                                  ),
                                );
                              },
                              isLight: true,
                            ),
                          ],
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Image.asset(
                                'assets/logo.png',
                                height: 32,
                                fit: BoxFit.contain,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Main Content with Enhanced Background
                  SliverToBoxAdapter(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: backgroundGradient,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          children: [
                            const SizedBox(height: 16),
                            
                            // Enhanced Greeting Section
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                gradient: primaryGradient,
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: primaryGradient.colors.first.withOpacity(0.2),
                                    blurRadius: 10,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          color: Colors.white.withOpacity(0.2),
                                          borderRadius: BorderRadius.circular(16),
                                        ),
                                        child: const Icon(
                                          Icons.person_outline,
                                          color: Colors.white,
                                          size: 28,
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'مرحباً، ${_currentDriver?.name ?? 'السائق'}',
                                              style: const TextStyle(
                                                fontSize: 24,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              _getGreetingMessage(),
                                              style: TextStyle(
                                                fontSize: 16,
                                                color: Colors.white.withOpacity(0.9),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 16),

                            // Enhanced Vehicle Status Section
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 10,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(12),
                                            decoration: BoxDecoration(
                                              gradient: primaryGradient,
                                              borderRadius: BorderRadius.circular(16),
                                            ),
                                            child: const Icon(
                                              Icons.directions_car,
                                              color: Colors.white,
                                              size: 24,
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          const Tooltip(
                                            message: 'Your vehicle\'s current status.',
                                            child: Text(
                                              'حالة المركبة',
                                              style: TextStyle(
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                                color: AppTheme.textDark,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      TextButton.icon(
                                        onPressed: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) => const VehicleScreen(),
                                            ),
                                          );
                                        },
                                        icon: const Icon(Icons.arrow_forward_ios, size: 16),
                                        label: const Text('عرض الكل'),
                                        style: TextButton.styleFrom(
                                          foregroundColor: primaryGradient.colors.first,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 20),
                                  isPortrait || ResponsiveSize.screenWidth < 600
                                      ? _buildStatusItemsPortrait()
                                      : _buildStatusItemsLandscape(),
                                ],
                              ),
                            ),

                            const SizedBox(height: 16),

                            // Enhanced Driving Assessment Section
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 10,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(12),
                                        decoration: BoxDecoration(
                                          gradient: primaryGradient,
                                          borderRadius: BorderRadius.circular(16),
                                        ),
                                        child: const Icon(
                                          Icons.analytics,
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      const Tooltip(
                                        message: 'Your overall driving score based on recent trips.',
                                        child: Text(
                                          'تقييم القيادة',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                            color: AppTheme.textDark,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 20),
                                  Row(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Container(
                                        width: 120,
                                        height: 120,
                                        margin: const EdgeInsets.only(right: 0),
                                        child: Stack(
                                          alignment: Alignment.center,
                                          children: [
                                            Container(
                                              width: 120,
                                              height: 120,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                gradient: primaryGradient,
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: primaryGradient.colors.first.withOpacity(0.3),
                                                    blurRadius: 10,
                                                    offset: const Offset(0, 4),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Container(
                                              width: 110,
                                              height: 110,
                                              decoration: const BoxDecoration(
                                                shape: BoxShape.circle,
                                                color: Colors.white,
                                              ),
                                              child: Center(
                                                child: Tooltip(
                                                  message: 'Your current driving score.',
                                                  child: Text(
                                                    '${_currentDriver?.driverScore.toStringAsFixed(0) ?? '0'}%',
                                                    style: TextStyle(
                                                      fontSize: 32,
                                                      fontWeight: FontWeight.bold,
                                                      color: primaryGradient.colors.first,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Expanded(
                                        child: Padding(
                                          padding: const EdgeInsets.only(left: 0),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              _buildDrivingAssessmentItem(
                                                'السلامة',
                                                _currentDriver?.scoreGrade ?? 'غير محدد',
                                                _getSafetyGradeColor(_currentDriver?.scoreGrade),
                                              ),
                                              const SizedBox(height: 10),
                                              const Divider(height: 1, color: Colors.grey),
                                              const SizedBox(height: 10),
                                              _buildDrivingAssessmentItem(
                                                'استهلاك الوقود',
                                                _getFuelEfficiencyGrade(),
                                                _getFuelEfficiencyGradeColor(_getFuelEfficiencyGrade()),
                                              ),
                                              const SizedBox(height: 10),
                                              const Divider(height: 1, color: Colors.grey),
                                              const SizedBox(height: 10),
                                              _buildDrivingAssessmentItem(
                                                'نمط القيادة',
                                                _getDrivingStyleGrade(),
                                                _getDrivingStyleGradeColor(_getDrivingStyleGrade()),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 16),

                            // Enhanced Last Trip Section
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.05),
                                    blurRadius: 10,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(8),
                                            decoration: BoxDecoration(
                                              gradient: primaryGradient,
                                              borderRadius: BorderRadius.circular(16),
                                            ),
                                            child: const Icon(
                                              Icons.map,
                                              color: Colors.white,
                                              size: 24,
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          const Text(
                                            'آخر الرحلات',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: AppTheme.textDark,
                                            ),
                                          ),
                                        ],
                                      ),
                                      if (_recentTrips.isNotEmpty)
                                        TextButton.icon(
                                          onPressed: () => _navigateToTripDetails(_recentTrips.first),
                                          icon: const Icon(Icons.arrow_forward_ios, size: 16),
                                          label: const Text('عرض التفاصيل'),
                                          style: TextButton.styleFrom(
                                            foregroundColor: primaryGradient.colors.first,
                                          ),
                                        ),
                                    ],
                                  ),
                                  const SizedBox(height: 20),
                                  // Enhanced Map Section
                                  Container(
                                    height: 160,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.black.withOpacity(0.1),
                                          blurRadius: 10,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(16),
                                      child: _buildTripMapWidget(),
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(16),
                                      border: Border.all(color: Colors.grey.withOpacity(0.1), width: 1),
                                    ),
                                    child: _buildLastTripContent(),
                                  ),
                                  if (_recentTrips.isEmpty) ...[
                                    const SizedBox(height: 12),
                                    ElevatedButton.icon(
                                      onPressed: () {
                                        // Navigate to trips screen to start a new trip
                                        Navigator.pushNamed(context, '/trips');
                                      },
                                      icon: const Icon(Icons.add_road),
                                      label: const Text('ابدأ رحلة جديدة'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: const Color(0xFFFFA500),
                                        foregroundColor: Colors.white,
                                        elevation: 0,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                                        ),
                                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),

                            const SizedBox(height: 16),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _getGreetingMessage() {
    if (_currentDriver == null) {
      return 'مرحباً بك في تطبيق درايفتي';
    }

    final vehicleCount = _currentDriver!.vehicles.length;
    final activeTrips = _recentTrips
        .where((trip) => trip.status == TripStatus.IN_PROGRESS)
        .length;

    if (activeTrips > 0) {
      return 'لديك رحلة نشطة حالياً';
    } else if (vehicleCount > 0) {
      return 'مركباتك في حالة جيدة اليوم';
    } else {
      return 'ابدأ بإضافة مركبتك الأولى';
    }
  }

  String _getFuelEfficiencyGrade() {
    if (_currentDriver == null || _currentDriver!.vehicles.isEmpty) {
      return 'غير محدد';
    }

    final avgFuelLevel = _currentDriver!.vehicles
            .map((v) => v.fuelLevel)
            .reduce((a, b) => a + b) /
        _currentDriver!.vehicles.length;

    if (avgFuelLevel > 75) {
      return 'ممتاز';
    } else if (avgFuelLevel > 50) {
      return 'جيد جداً';
    } else if (avgFuelLevel > 25) {
      return 'جيد';
    } else {
      return 'يحتاج تحسين';
    }
  }

  String _getDrivingStyleGrade() {
    if (_currentDriver == null) {
      return 'غير محدد';
    }

    final score = _currentDriver!.driverScore;
    if (score >= 90) {
      return 'مثالي';
    } else if (score >= 80) {
      return 'ممتاز';
    } else if (score >= 70) {
      return 'جيد جداً';
    } else if (score >= 60) {
      return 'جيد';
    } else {
      return 'يحتاج تحسين';
    }
  }

  Color _getSafetyGradeColor(String? grade) {
    if (grade == null || grade == 'غير محدد') return AppTheme.textMedium;
    if (grade == 'ممتاز' || grade == 'جيد جداً') return AppTheme.accentGreen;
    if (grade == 'جيد') return AppTheme.accentYellow;
    return AppTheme.accentRed;
  }

  Color _getFuelEfficiencyGradeColor(String grade) {
    if (grade == 'غير محدد') return AppTheme.textMedium;
    if (grade == 'ممتاز' || grade == 'جيد جداً') return AppTheme.accentGreen;
    if (grade == 'جيد') return AppTheme.accentYellow;
    return AppTheme.accentRed;
  }

  Color _getDrivingStyleGradeColor(String grade) {
    if (grade == 'غير محدد') return AppTheme.textMedium;
    if (grade == 'مثالي' || grade == 'ممتاز') return AppTheme.accentGreen;
    if (grade == 'جيد جداً') return AppTheme.accentYellow;
    if (grade == 'جيد') return AppTheme.accentYellow;
    return AppTheme.accentRed;
  }

  Widget _buildLastTripContent() {
    if (_recentTrips.isEmpty) {
      return Column(
        children: [
          Container(
            padding: ResponsiveSize.paddingAll(4),
            decoration: BoxDecoration(
              color: const Color(0xFFFFF5E6),
              borderRadius: BorderRadius.circular(ResponsiveSize.width(2)),
              border: Border.all(
                color: Colors.grey.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.directions_car_outlined,
                  size: 40,
                  color: AppTheme.textMedium,
                ),
                SizedBox(height: ResponsiveSize.height(2.5)),
                Text(
                  'لا توجد رحلات سابقة',
                  style: TextStyle(
                    fontSize: ResponsiveSize.fontSize(3.8),
                    color: AppTheme.textDark,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: ResponsiveSize.height(1.5)),
                Text(
                  'ابدأ رحلتك الأولى لتظهر هنا',
                  style: TextStyle(
                    fontSize: ResponsiveSize.fontSize(3.5),
                    color: AppTheme.textMedium,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    final lastTrip = _recentTrips.first;
    final duration = lastTrip.endTime != null
        ? lastTrip.endTime!.difference(lastTrip.startTime)
        : Duration.zero;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: ResponsiveSize.paddingAll(1.5),
              decoration: const BoxDecoration(
                color: Color(0xFFFFF0E8),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.location_on,
                color: AppTheme.primaryColor,
                size: 20,
              ),
            ),
            SizedBox(width: ResponsiveSize.width(2)),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'من: ${lastTrip.startLocation}',
                    style: TextStyle(
                      fontSize: ResponsiveSize.fontSize(3.5),
                      color: AppTheme.textDark,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (lastTrip.endLocation != null)
                    Text(
                      'إلى: ${lastTrip.endLocation}',
                      style: TextStyle(
                        fontSize: ResponsiveSize.fontSize(3.5),
                        color: AppTheme.textDark,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: ResponsiveSize.height(1.5)),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: ResponsiveSize.paddingAll(1.5),
              decoration: const BoxDecoration(
                color: Color(0xFFFFF0E8),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.access_time,
                color: AppTheme.primaryColor,
                size: 20,
              ),
            ),
            SizedBox(width: ResponsiveSize.width(2)),
            Expanded(
              child: Text(
                'المسافة: ${lastTrip.distance?.toStringAsFixed(1) ?? '0.0'} كم · الوقت: ${duration.inMinutes} دقيقة',
                style: TextStyle(
                  fontSize: ResponsiveSize.fontSize(3.5),
                  color: AppTheme.textDark,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Color _getFuelLevelColor(double fuelLevel) {
    if (fuelLevel > 50) {
      return AppTheme.accentGreen;
    } else if (fuelLevel > 25) {
      return AppTheme.accentYellow;
    } else {
      return AppTheme.accentRed;
    }
  }

  Color _getVehicleStatusColor(VehicleStatus status) {
    switch (status) {
      case VehicleStatus.ACTIVE:
        return AppTheme.accentGreen;
      case VehicleStatus.MAINTENANCE:
        return AppTheme.accentYellow;
      case VehicleStatus.INACTIVE:
        return AppTheme.accentRed;
    }
  }

  Color _getInsuranceColor(DateTime insuranceExpiry) {
    final daysUntilExpiry = insuranceExpiry.difference(DateTime.now()).inDays;
    if (daysUntilExpiry > 30) {
      return AppTheme.accentGreen;
    } else if (daysUntilExpiry > 7) {
      return AppTheme.accentYellow;
    } else {
      return AppTheme.accentRed;
    }
  }

  String _getInsuranceStatus(DateTime insuranceExpiry) {
    final daysUntilExpiry = insuranceExpiry.difference(DateTime.now()).inDays;
    if (daysUntilExpiry < 0) {
      return 'منتهي';
    } else if (daysUntilExpiry <= 7) {
      return 'ينتهي قريباً';
    } else if (daysUntilExpiry <= 30) {
      return '$daysUntilExpiry يوم';
    } else {
      return 'ساري';
    }
  }

  Widget _buildTripMapWidget() {
    if (_recentTrips.isEmpty) {
      return Container(
        height: ResponsiveSize.height(15),
        decoration: BoxDecoration(
          color: AppTheme.backgroundLight,
          borderRadius: BorderRadius.circular(ResponsiveSize.width(2)),
          border: Border.all(
            color: Colors.grey.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.map_outlined,
              size: ResponsiveSize.width(12),
              color: AppTheme.textMedium,
            ),
            SizedBox(height: ResponsiveSize.height(2)),
            Text(
              'لا توجد رحلات لعرضها على الخريطة',
              style: TextStyle(
                fontSize: ResponsiveSize.fontSize(4),
                color: AppTheme.textMedium,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    final lastTrip = _recentTrips.first;

    // Use real coordinates if available, otherwise use default Riyadh coordinates
    final startLat = lastTrip.startLatitude ?? 24.7136;
    final startLng = lastTrip.startLongitude ?? 46.6753;
    final endLat = lastTrip.endLatitude ?? startLat + 0.01;
    final endLng = lastTrip.endLongitude ?? startLng + 0.02;

    return GestureDetector(
      onTap: () => _navigateToTripDetails(lastTrip),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(ResponsiveSize.width(2)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(ResponsiveSize.width(2)),
          child: OpenStreetMapWidget(
            height: ResponsiveSize.height(15),
            initialLatitude: startLat,
            initialLongitude: startLng,
            zoom: 13,
            interactive: false,
            markers: [
              MapMarker(
                latitude: startLat,
                longitude: startLng,
                label: 'البداية',
                icon: Icons.location_on,
                color: AppTheme.accentGreen,
              ),
              if (lastTrip.endLatitude != null && lastTrip.endLongitude != null)
                MapMarker(
                  latitude: endLat,
                  longitude: endLng,
                  label: 'النهاية',
                  icon: Icons.location_on,
                  color: AppTheme.accentRed,
                ),
            ],
            route: lastTrip.endLatitude != null && lastTrip.endLongitude != null
                ? MapRoute(
                    points: [
                      RoutePoint(latitude: startLat, longitude: startLng),
                      RoutePoint(latitude: endLat, longitude: endLng),
                    ],
                    color: AppTheme.primaryColor,
                    width: 4.0,
                  )
                : null,
          ),
        ),
      ),
    );
  }

  Widget _buildTripDetailsButton() {
    if (_recentTrips.isEmpty) {
      return ElevatedButton.icon(
        onPressed: () {
          // Navigate to trips screen to start a new trip
          Navigator.pushNamed(context, '/trips');
        },
        icon: const Icon(Icons.add_road),
        label: const Text('ابدأ رحلة جديدة'),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFFA500),
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      );
    }

    final lastTrip = _recentTrips.first;

    return ElevatedButton.icon(
      onPressed: () => _navigateToTripDetails(lastTrip),
      icon: const Icon(Icons.map),
      label: const Text('عرض التفاصيل'),
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFFFFA500),
        foregroundColor: Colors.white,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    );
  }

  void _navigateToTripDetails(Trip trip) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TripDetailsScreen(trip: trip),
      ),
    );
  }



  Widget _buildHeaderButton({
    required IconData icon,
    required VoidCallback onTap,
    bool isLight = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          onTap();
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isLight ? Colors.white.withOpacity(0.6) : const Color(0xFFFFF5E6),
            borderRadius: BorderRadius.circular(16),
            boxShadow: isLight ? null : [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            icon,
            color: isLight ? const Color(0xFFFFA500) : const Color(0xFFFFA500),
            size: 24,
          ),
        ),
      ),
    );
  }

  Widget _buildStatusItemsPortrait() {
    if (_currentDriver == null || _currentDriver!.vehicles.isEmpty) {
      return Container(
        padding: ResponsiveSize.paddingAll(4),
        decoration: BoxDecoration(
          color: AppTheme.backgroundLight,
          borderRadius: BorderRadius.circular(ResponsiveSize.width(2)),
          border: Border.all(
            color: Colors.grey.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: ResponsiveSize.paddingAll(3),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.05),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.directions_car_outlined,
                size: ResponsiveSize.width(ResponsiveSize.isTablet ? 8 : 12),
                color: AppTheme.primaryColor,
              ),
            ),
            SizedBox(height: ResponsiveSize.height(2)),
            Text(
              'لا توجد مركبات',
              style: TextStyle(
                fontSize: ResponsiveSize.fontSize(4),
                color: AppTheme.textDark,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: ResponsiveSize.height(1)),
            Text(
              'قم بإضافة مركبتك الأولى للبدء',
              style: TextStyle(
                fontSize: ResponsiveSize.fontSize(3.5),
                color: AppTheme.textMedium,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: ResponsiveSize.height(2)),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const VehicleScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.directions_car),
              label: const Text('إضافة مركبة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: ResponsiveSize.paddingSymmetric(horizontal: 6, vertical: 2),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(ResponsiveSize.width(3)),
                ),
                elevation: 0,
              ),
            ),
          ],
        ),
      );
    }

    final vehicle = _currentDriver!.vehicles.first;
    final fuelLevel = vehicle.fuelLevel;

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: ResponsiveSize.isTablet ? 4 : 2,
      crossAxisSpacing: ResponsiveSize.width(2),
      mainAxisSpacing: ResponsiveSize.height(1.5),
      childAspectRatio: ResponsiveSize.isTablet ? 1.0 : 1.1,
      children: [
        _buildStatusItem(
            icon: Icons.local_gas_station,
            iconColor: _getFuelLevelColor(fuelLevel),
            title: 'مستوى الوقود',
            value: '${fuelLevel.toStringAsFixed(0)}%'),
        _buildStatusItem(
            icon: Icons.directions_car,
            iconColor: _getVehicleStatusColor(vehicle.status),
            title: 'حالة المركبة',
            value: vehicle.statusDisplayName),
        _buildStatusItem(
            icon: Icons.calendar_today,
            iconColor: _getInsuranceColor(vehicle.insuranceExpiry),
            title: 'التأمين',
            value: _getInsuranceStatus(vehicle.insuranceExpiry)),
        _buildStatusItem(
            icon: Icons.build,
            iconColor: AppTheme.accentYellow,
            title: 'نوع الوقود',
            value: vehicle.fuelTypeDisplayName),
      ],
    );
  }

  Widget _buildStatusItemsLandscape() {
    if (_currentDriver == null || _currentDriver!.vehicles.isEmpty) {
      return Container(
        padding: ResponsiveSize.paddingAll(4),
        decoration: BoxDecoration(
          color: AppTheme.backgroundLight,
          borderRadius: BorderRadius.circular(ResponsiveSize.width(2)),
          border: Border.all(
            color: Colors.grey.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: ResponsiveSize.paddingAll(3),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.05),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.directions_car_outlined,
                size: ResponsiveSize.width(ResponsiveSize.isTablet ? 8 : 12),
                color: AppTheme.primaryColor,
              ),
            ),
            SizedBox(height: ResponsiveSize.height(2)),
            Text(
              'لا توجد مركبات',
              style: TextStyle(
                fontSize: ResponsiveSize.fontSize(4),
                color: AppTheme.textDark,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: ResponsiveSize.height(1)),
            Text(
              'قم بإضافة مركبتك الأولى للبدء',
              style: TextStyle(
                fontSize: ResponsiveSize.fontSize(3.5),
                color: AppTheme.textMedium,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: ResponsiveSize.height(2)),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const VehicleScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.directions_car),
              label: const Text('إضافة مركبة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: ResponsiveSize.paddingSymmetric(horizontal: 6, vertical: 2),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(ResponsiveSize.width(3)),
                ),
                elevation: 0,
              ),
            ),
          ],
        ),
      );
    }

    final vehicle = _currentDriver!.vehicles.first;
    final fuelLevel = vehicle.fuelLevel;

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: ResponsiveSize.isTablet ? 4 : 4, // 1x4 grid for landscape
      crossAxisSpacing: ResponsiveSize.width(1.5),
      mainAxisSpacing: ResponsiveSize.height(1.5),
      childAspectRatio: ResponsiveSize.isTablet ? 1.0 : 0.9,
      children: [
         _buildStatusItem(
                icon: Icons.local_gas_station,
                iconColor: _getFuelLevelColor(fuelLevel),
                title: 'مستوى الوقود',
                value: '${fuelLevel.toStringAsFixed(0)}%'),
            _buildStatusItem(
                icon: Icons.directions_car,
                iconColor: _getVehicleStatusColor(vehicle.status),
                title: 'حالة المركبة',
                value: vehicle.statusDisplayName),
            _buildStatusItem(
                icon: Icons.calendar_today,
                iconColor: _getInsuranceColor(vehicle.insuranceExpiry),
                title: 'التأمين',
                value: _getInsuranceStatus(vehicle.insuranceExpiry)),
            _buildStatusItem(
                icon: Icons.build,
                iconColor: AppTheme.accentYellow,
                title: 'نوع الوقود',
                value: vehicle.fuelTypeDisplayName),
      ],
    );
  }

  Widget _buildStatusItem(
      {required IconData icon,
      required Color iconColor,
      required String title,
      required String value}) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          // Add your onTap logic here
        },
        borderRadius: BorderRadius.circular(ResponsiveSize.width(3)),
        child: Container(
          padding: ResponsiveSize.paddingAll(3),
          decoration: BoxDecoration(
            color: AppTheme.backgroundWhite,
            borderRadius: BorderRadius.circular(ResponsiveSize.width(3)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: ResponsiveSize.width(2),
                offset: Offset(0, ResponsiveSize.height(0.5)),
              ),
            ],
            border: Border.all(
              color: Colors.grey.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: ResponsiveSize.paddingAll(2.5),
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: ResponsiveSize.width(ResponsiveSize.isTablet ? 5 : 6),
                ),
              ),
              SizedBox(height: ResponsiveSize.height(1.5)),
              Text(
                title,
                style: TextStyle(
                  fontSize: ResponsiveSize.fontSize(ResponsiveSize.isTablet ? 3.5 : 3.2),
                  color: AppTheme.textDark,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: ResponsiveSize.height(1)),
              Text(
                value,
                style: TextStyle(
                  fontSize: ResponsiveSize.fontSize(ResponsiveSize.isTablet ? 4.5 : 4),
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textDark,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDrivingAssessmentItem(
      String label, String value, Color valueColor) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: ResponsiveSize.fontSize(3.5),
              color: AppTheme.textDark,
              fontWeight: FontWeight.w600,
            ),
          ),
         
          Text(
            value,
            style: TextStyle(
              fontSize: ResponsiveSize.fontSize(3.5),
              fontWeight: FontWeight.normal,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }

}

// صفحات التنقل الأساسية
class trips_screen extends StatelessWidget {
  const trips_screen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('صفحة الرحلات')),
    );
  }
}

class reports_screen extends StatelessWidget {
  const reports_screen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('صفحة التقارير')),
    );
  }
}

class settings_page extends StatelessWidget {
  const settings_page({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('صفحة الإعدادات')),
    );
  }
}
