group 'io.github.edufolly.flutterbluetoothserial'
version '1.0-SNAPSHOT'
buildscript {
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.0'
    }
}
rootProject.allprojects {
    repositories {
        google()
        jcenter()
    }
}
apply plugin: 'com.android.library'
android {
    namespace 'io.github.edufolly.flutterbluetoothserial'
    compileSdkVersion 30
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    defaultConfig {
        minSdkVersion 19
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
    dependencies {
        implementation 'androidx.appcompat:appcompat:1.3.0'
    }
    buildToolsVersion '30.0.3'
}

dependencies {
}
