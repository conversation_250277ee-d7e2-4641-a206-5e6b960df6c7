import 'package:flutter/material.dart';

class ResponsiveSize {
  static MediaQueryData? _mediaQueryData;
  static double? _screenWidth;
  static double? _screenHeight;
  static double? _blockSizeHorizontal;
  static double? _blockSizeVertical;
  static double? _safeAreaHorizontal;
  static double? _safeAreaVertical;
  static double? _safeBlockHorizontal;
  static double? _safeBlockVertical;
  static double? _textScaleFactor;
  static bool? _isTablet;
  static bool _isInitialized = false;

  // Check if ResponsiveSize has been initialized
  static bool get isInitialized => _isInitialized;

  // Getters with fallback values
  static double get screenWidth => _screenWidth ?? 375.0;
  static double get screenHeight => _screenHeight ?? 812.0;
  static double get blockSizeHorizontal => _blockSizeHorizontal ?? 3.75;
  static double get blockSizeVertical => _blockSizeVertical ?? 8.12;
  static double get safeBlockHorizontal => _safeBlockHorizontal ?? 3.75;
  static double get safeBlockVertical => _safeBlockVertical ?? 8.12;
  static double get textScaleFactor => _textScaleFactor ?? 1.0;
  static bool get isTablet => _isTablet ?? false;

  void init(BuildContext context) {
    _mediaQueryData = MediaQuery.of(context);
    _screenWidth = _mediaQueryData!.size.width;
    _screenHeight = _mediaQueryData!.size.height;
    _blockSizeHorizontal = _screenWidth! / 100;
    _blockSizeVertical = _screenHeight! / 100;

    _safeAreaHorizontal = _mediaQueryData!.padding.left + _mediaQueryData!.padding.right;
    _safeAreaVertical = _mediaQueryData!.padding.top + _mediaQueryData!.padding.bottom;
    _safeBlockHorizontal = (_screenWidth! - _safeAreaHorizontal!) / 100;
    _safeBlockVertical = (_screenHeight! - _safeAreaVertical!) / 100;
    _textScaleFactor = _mediaQueryData!.textScaler.scale(1.0);

    // Determine if device is a tablet based on shortest side
    _isTablet = _mediaQueryData!.size.shortestSide >= 600;
    _isInitialized = true;
  }

  // Get responsive font size
  static double fontSize(double size) {
    return size * safeBlockHorizontal * (isTablet ? 0.8 : 1.0);
  }

  // Get responsive height
  static double height(double height) {
    return height * safeBlockVertical;
  }

  // Get responsive width
  static double width(double width) {
    return width * safeBlockHorizontal;
  }

  // Get responsive padding
  static EdgeInsets padding(double left, double top, double right, double bottom) {
    return EdgeInsets.fromLTRB(
      width(left),
      height(top),
      width(right),
      height(bottom),
    );
  }

  // Get responsive padding all
  static EdgeInsets paddingAll(double padding) {
    return EdgeInsets.all(width(padding));
  }

  // Get responsive symmetric padding
  static EdgeInsets paddingSymmetric({double horizontal = 0, double vertical = 0}) {
    return EdgeInsets.symmetric(
      horizontal: width(horizontal),
      vertical: height(vertical),
    );
  }

  // Get responsive size (width and height)
  static Size size(double width, double height) {
    return Size(
      ResponsiveSize.width(width),
      ResponsiveSize.height(height),
    );
  }
}
