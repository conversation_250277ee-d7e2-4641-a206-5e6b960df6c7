import 'package:flutter/material.dart';

class ResponsiveSize {
  static late MediaQueryData _mediaQueryData;
  static late double screenWidth;
  static late double screenHeight;
  static late double blockSizeHorizontal;
  static late double blockSizeVertical;
  static late double _safeAreaHorizontal;
  static late double _safeAreaVertical;
  static late double safeBlockHorizontal;
  static late double safeBlockVertical;
  static late double textScaleFactor;
  static late bool isTablet;

  void init(BuildContext context) {
    _mediaQueryData = MediaQuery.of(context);
    screenWidth = _mediaQueryData.size.width;
    screenHeight = _mediaQueryData.size.height;
    blockSizeHorizontal = screenWidth / 100;
    blockSizeVertical = screenHeight / 100;

    _safeAreaHorizontal = _mediaQueryData.padding.left + _mediaQueryData.padding.right;
    _safeAreaVertical = _mediaQueryData.padding.top + _mediaQueryData.padding.bottom;
    safeBlockHorizontal = (screenWidth - _safeAreaHorizontal) / 100;
    safeBlockVertical = (screenHeight - _safeAreaVertical) / 100;
    textScaleFactor = _mediaQueryData.textScaleFactor;
    
    // Determine if device is a tablet based on shortest side
    isTablet = _mediaQueryData.size.shortestSide >= 600;
  }

  // Get responsive font size
  static double fontSize(double size) {
    return size * safeBlockHorizontal * (isTablet ? 0.8 : 1.0);
  }

  // Get responsive height
  static double height(double height) {
    return height * safeBlockVertical;
  }

  // Get responsive width
  static double width(double width) {
    return width * safeBlockHorizontal;
  }

  // Get responsive padding
  static EdgeInsets padding(double left, double top, double right, double bottom) {
    return EdgeInsets.fromLTRB(
      width(left),
      height(top),
      width(right),
      height(bottom),
    );
  }

  // Get responsive padding all
  static EdgeInsets paddingAll(double padding) {
    return EdgeInsets.all(width(padding));
  }

  // Get responsive symmetric padding
  static EdgeInsets paddingSymmetric({double horizontal = 0, double vertical = 0}) {
    return EdgeInsets.symmetric(
      horizontal: width(horizontal),
      vertical: height(vertical),
    );
  }

  // Get responsive size (width and height)
  static Size size(double width, double height) {
    return Size(
      ResponsiveSize.width(width),
      ResponsiveSize.height(height),
    );
  }
}
