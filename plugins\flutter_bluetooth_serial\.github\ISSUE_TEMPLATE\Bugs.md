---
name: <PERSON>ug report
about: Create a report to help us improve
label: "Type: Bug"

---

<!-- 
    Welcome to the issues page of Flutter Bluetooth Serial library.

    This is a template of issue for bugs, prepared to avoid some further questions. 

    Before posting, please search trough other related issues - especially check pinned ones.

    If you manage to find solution by your own, don't remove this issue file - please share the knowledge to other people, which may come across same problem in future.
-->

### Problem summary

<!-- 
    Describe the problem. Helpful might be current behavior, excepted behaviour and/or what you tried to do on your own to solve the issue.
    Example: "My Raspberry Pi could not be discovered using the library neither the example app. On other hand, running discovery from system built-in app for Bluetooth shows the device - so I am sure it is a problem with the library."
-->

### Steps to reproduce

<!-- Please describe how you experienced bug to happen. It may be steps list or a code snippet you used. -->

1. <!-- First step, example: "Open example app" -->
2. <!-- Second step, example: "Turn on Bluetooth" -->
3. <!-- First step, example: "Open discovery page via button" -->
4. <!-- More step if necessary, example: "Wait for discovery to finish" -->

<!-- If you have some code to ease the reproduction of the bug please share it -->

### Environment

<!-- Share your output of running `flutter --version` in console` and `adb shell "getprop | grep ro.build"` in snippet below -->
```
```

<!-- In case, if the bug might be also related to other devices, please share details of these. For example Raspberry Pi: configuration, version of the OS, libraries used. -->

