import 'package:flutter/material.dart';
import 'services/auth_service.dart';
import 'loginscreen.dart';
import 'homep_fixed.dart';
import 'utils/app_theme.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    try {
      // Initialize auth service
      await _authService.initialize();
      
      // Check if user is logged in
      final isLoggedIn = await _authService.isLoggedIn();
      
      // Add a small delay for better UX
      await Future.delayed(const Duration(milliseconds: 1500));
      
      if (mounted) {
        if (isLoggedIn) {
          // Navigate to home page
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomePage()),
          );
        } else {
          // Navigate to login page
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const LoginScreen()),
          );
        }
      }
    } catch (e) {
      // If there's an error, go to login screen
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    }
  }

  @override
  void dispose() {
    _authService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: AppTheme.primaryLightColor,
                  borderRadius: BorderRadius.circular(AppTheme.borderRadiusXLarge),
                ),
                child: const Icon(
                  Icons.directions_car,
                  color: AppTheme.primaryColor,
                  size: 64,
                ),
              ),
              const SizedBox(height: 24),
              
              // App Name
              Text(
                'Drivety',
                style: AppTheme.getTitleStyle().copyWith(color: AppTheme.primaryColor),
              ),
              const SizedBox(height: 8),
              
              // Subtitle
              Text(
                'تطبيق مراقبة حالة سيارتك',
                style: AppTheme.getBodyStyle(),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              
              // Loading indicator
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
